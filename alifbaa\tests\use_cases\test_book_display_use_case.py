from unittest.mock import MagicMock

import pytest

from alifbaa.ir.book import Book, BookMetadata
from alifbaa.ir.chapter import Chapter
from alifbaa.repositories.book_repository import BookRepository
from alifbaa.use_cases.book_display_use_case import BookDisplayUseCase
from alifbaa.workers.text_operations import TextOperations


class TestBookDisplayUseCase:
    @pytest.fixture
    def mock_book_repository(self) -> MagicMock:
        repo = MagicMock(spec=BookRepository)
        return repo

    @pytest.fixture
    def mock_text_operations(self) -> MagicMock:
        text_ops = MagicMock(spec=TextOperations)
        return text_ops

    def test_get_available_books_returns_list_of_dicts(
        self, mock_book_repository: MagicMock, mock_text_operations: MagicMock
    ) -> None:
        metadata1 = BookMetadata(id="book1", title="Book 1", language="en")
        metadata2 = BookMetadata(id="book2", title="Book 2", language="ar")
        mock_book_repository.get_available_books.return_value = [metadata1, metadata2]

        use_case = BookDisplayUseCase(mock_book_repository, mock_text_operations)

        result = use_case.get_available_books()

        assert len(result) == 2
        assert result[0].id == "book1"
        assert result[1].id == "book2"
        mock_book_repository.get_available_books.assert_called_once()

    def test_get_book_metadata_returns_dict(
        self, mock_book_repository: MagicMock, mock_text_operations: MagicMock
    ) -> None:
        metadata = BookMetadata(id="book1", title="Book 1", language="en")
        mock_book_repository.get_book_metadata.return_value = metadata

        use_case = BookDisplayUseCase(mock_book_repository, mock_text_operations)

        result = use_case.get_book_metadata("book1")

        assert result is not None
        assert result.id == "book1"
        assert result.title == "Book 1"
        mock_book_repository.get_book_metadata.assert_called_once_with("book1")

    def test_get_book_metadata_returns_none_for_nonexistent_book(
        self, mock_book_repository: MagicMock, mock_text_operations: MagicMock
    ) -> None:
        mock_book_repository.get_book_metadata.return_value = None

        use_case = BookDisplayUseCase(mock_book_repository, mock_text_operations)

        result = use_case.get_book_metadata("nonexistent")

        assert result is None
        mock_book_repository.get_book_metadata.assert_called_once_with("nonexistent")

    def test_get_book_returns_dict(
        self, mock_book_repository: MagicMock, mock_text_operations: MagicMock
    ) -> None:
        metadata = BookMetadata(id="book1", title="Book 1", language="en")
        chapter = Chapter(number=1, name="Chapter 1")
        book = Book(metadata=metadata, chapters=(chapter,))
        mock_book_repository.get_book.return_value = book

        use_case = BookDisplayUseCase(mock_book_repository, mock_text_operations)

        result = use_case.get_book("book1")

        assert result is not None
        assert result.metadata.id == "book1"
        assert len(result.chapters) == 1
        mock_book_repository.get_book.assert_called_once_with("book1")

    def test_get_book_returns_none_for_nonexistent_book(
        self, mock_book_repository: MagicMock, mock_text_operations: MagicMock
    ) -> None:
        mock_book_repository.get_book.return_value = None

        use_case = BookDisplayUseCase(mock_book_repository, mock_text_operations)

        result = use_case.get_book("nonexistent")

        assert result is None
        mock_book_repository.get_book.assert_called_once_with("nonexistent")

    def test_get_chapter_returns_dict(
        self, mock_book_repository: MagicMock, mock_text_operations: MagicMock
    ) -> None:
        chapter = Chapter(number=1, name="Chapter 1")
        mock_book_repository.get_chapter.return_value = chapter

        use_case = BookDisplayUseCase(mock_book_repository, mock_text_operations)

        result = use_case.get_chapter("book1", 1)

        assert result is not None
        assert result.number == 1
        assert result.name == "Chapter 1"
        mock_book_repository.get_chapter.assert_called_once_with("book1", 1)

    def test_get_chapter_returns_none_for_nonexistent_chapter(
        self, mock_book_repository: MagicMock, mock_text_operations: MagicMock
    ) -> None:
        mock_book_repository.get_chapter.return_value = None

        use_case = BookDisplayUseCase(mock_book_repository, mock_text_operations)

        result = use_case.get_chapter("book1", 999)

        assert result is None
        mock_book_repository.get_chapter.assert_called_once_with("book1", 999)
