import os
import tempfile
from unittest.mock import patch

import pytest

from alifbaa.config import (
    get_config_file,
    get_flask_config,
    get_parser,
    get_books_config,
)


@pytest.fixture
def mock_config_file():
    with tempfile.NamedTemporaryFile(mode="w+", delete=False, suffix=".cfg") as f:
        f.write("[flask]\n")
        f.write("secret_key = test_secret_key\n")
        f.write("\n[books]\n")
        f.write("books_dir = /test/path/books\n")
        config_path = f.name

    yield config_path

    if os.path.exists(config_path):
        os.unlink(config_path)


@pytest.fixture
def mock_invalid_config_file():
    with tempfile.NamedTemporaryFile(mode="w+", delete=False, suffix=".cfg") as f:
        f.write("[flask]\n")
        f.write("secret_key = test_secret_key\n")
        f.write("\n[books]\n")
        # Missing required keys
        config_path = f.name

    yield config_path

    if os.path.exists(config_path):
        os.unlink(config_path)


def test_get_parser(mock_config_file):
    parser = get_parser(mock_config_file)

    assert parser.has_section("flask")
    assert parser.has_section("books")
    assert parser.get("flask", "secret_key") == "test_secret_key"
    assert parser.get("books", "books_dir") == "/test/path/books"


def test_get_flask_config(mock_config_file):
    flask_config = get_flask_config(mock_config_file)

    assert "secret_key" in flask_config
    assert flask_config["secret_key"] == "test_secret_key"


def test_get_books_config(mock_config_file):
    books_config = get_books_config(mock_config_file)

    assert "books_dir" in books_config
    assert books_config["books_dir"] == "/test/path/books"


@patch("platformdirs.user_config_dir")
@patch("os.makedirs")
@patch("os.environ")
def test_get_config_file(mock_environ, mock_makedirs, mock_user_config_dir):
    mock_user_config_dir.return_value = "/mock/config/dir"
    mock_environ.get.return_value = None

    expected_path = os.path.join("/mock/config/dir", "alifbaa.cfg")
    mock_environ.get.side_effect = lambda _, default: default

    config_file = get_config_file()

    assert config_file == expected_path
    mock_makedirs.assert_called_once_with("/mock/config/dir", exist_ok=True)
    mock_environ.get.assert_called_once_with("ALIFBAA_CONFIG", expected_path)


@patch("os.environ")
def test_get_config_file_with_env_var(mock_environ):
    mock_environ.get.return_value = "/custom/config/path.cfg"

    config_file = get_config_file()

    assert config_file == "/custom/config/path.cfg"
    mock_environ.get.assert_called_once()


@patch("alifbaa.config.get_config_file")
@patch("os.path.exists")
def test_parser_file_not_found(mock_exists, mock_get_config_file):
    mock_get_config_file.return_value = "/nonexistent/config.cfg"
    mock_exists.return_value = False

    with pytest.raises(FileNotFoundError):
        get_parser()


def test_get_books_config_missing_keys(mock_invalid_config_file):
    with pytest.raises(ValueError) as excinfo:
        get_books_config(mock_invalid_config_file)

    assert "Missing required keys" in str(excinfo.value)
    assert "books_dir" in str(excinfo.value)


@patch("alifbaa.config.get_parser")
def test_get_flask_config_missing_section(mock_get_parser):
    mock_parser = mock_get_parser.return_value
    mock_parser.has_section.return_value = False

    with pytest.raises(ValueError) as excinfo:
        get_flask_config()

    assert "Missing [flask] section" in str(excinfo.value)


@patch("alifbaa.config.get_parser")
def test_get_books_config_missing_section(mock_get_parser):
    mock_parser = mock_get_parser.return_value
    mock_parser.has_section.return_value = False

    with pytest.raises(ValueError) as excinfo:
        get_books_config()

    assert "Missing [books] section" in str(excinfo.value)
