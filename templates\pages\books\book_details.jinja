{% extends "base.jinja" %}

{% block title %}{{ book.metadata.title if book.metadata else 'كتاب' }}{% endblock %}

{% block content %}
<div class="book-details">
    <div class="navigation">
        <a href="/">العودة إلى الرئيسية</a>
    </div>
    
    <div class="book-header">
        {% if book.metadata %}
        <h1 class="book-title arabic-text">{{ book.metadata.title }}</h1>
        {% if book.metadata.author %}
        <div class="book-author">{{ book.metadata.author }}</div>
        {% endif %}
        {% if book.metadata.description %}
        <div class="book-description">{{ book.metadata.description }}</div>
        {% endif %}
        {% else %}
        <h1>كتاب</h1>
        {% endif %}
    </div>
    
    <div id="settings-panel">
        {% include "components/settings_panel.jinja" %}
    </div>
    
    <div class="chapters-list">
        {% if book and book.chapters %}
            <h2>الفصول</h2>
            <div class="chapters-grid">
                {% for chapter in book.chapters %}
                <div class="chapter-item">
                    <a href="/book/{{ book_id }}/chapter/{{ chapter.number }}">
                        <div class="chapter-number">{{ chapter.number }}</div>
                        <div class="chapter-name arabic-text">{{ chapter.name }}</div>
                        {% if chapter.verses %}
                        <div class="chapter-info">{{ chapter.verses|length }} آية</div>
                        {% endif %}
                    </a>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-chapters">
                <p>لا توجد فصول متاحة</p>
            </div>
        {% endif %}
    </div>
</div>

<style>
.book-details {
    direction: rtl;
}

.book-header {
    text-align: center;
    margin: 30px 0;
    padding: 30px;
    background: #f8f9fa;
    border-radius: 8px;
}

.book-title {
    font-size: 32px;
    color: #2c3e50;
    margin-bottom: 15px;
}

.book-author {
    font-size: 18px;
    color: #666;
    margin: 10px 0;
}

.book-description {
    font-size: 16px;
    color: #555;
    margin: 15px 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.chapters-list h2 {
    color: #2c3e50;
    margin: 30px 0 20px 0;
}

.chapters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.chapter-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chapter-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.chapter-item a {
    display: block;
    padding: 20px;
    text-decoration: none;
    color: inherit;
}

.chapter-number {
    background: #28a745;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 10px;
}

.chapter-name {
    font-size: 20px;
    font-weight: bold;
    margin: 10px 0;
    color: #2c3e50;
}

.chapter-info {
    font-size: 14px;
    color: #666;
    margin-top: 8px;
}

.no-chapters {
    text-align: center;
    padding: 40px;
    color: #666;
}
</style>
{% endblock %}
