<div class="word-details">
    <h3>تفاصيل الكلمة</h3>
    
    <div class="word-info">
        <div class="word-display">
            <h4>الكلمة:</h4>
            <div class="arabic-text" style="font-size: 32px; margin: 10px 0;">
                {{ word.text }}
            </div>
        </div>
        
        <div class="word-plain">
            <h4>بدون تشكيل:</h4>
            <div class="arabic-text" style="font-size: 28px; margin: 10px 0;">
                {{ word.text_plain }}
            </div>
        </div>
        
        <div class="word-position">
            <h4>الموضع:</h4>
            <p>رقم الكلمة: {{ word.position }}</p>
            {% if verse_num %}
            <p>رقم الآية: {{ verse_num }}</p>
            {% endif %}
        </div>
    </div>
    
    <div class="letters-breakdown">
        <h4>تحليل الحروف:</h4>
        <div class="letters-container">
            {% for i in range(letters|length) %}
            <div class="letter-item" style="display: inline-block; margin: 5px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                <div class="letter-with-diacritics" style="font-size: 24px;">{{ letters[i] }}</div>
                <div class="letter-plain" style="font-size: 18px; color: #666;">{{ letters_plain[i] if i < letters_plain|length else '' }}</div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <div class="tatweel-section" style="margin-top: 20px;">
        <h4>إضافة التطويل (الكشيدة):</h4>
        <div style="margin: 10px 0;">
            <button onclick="addTatweel('{{ word.text }}', 1)" style="margin: 5px; padding: 8px 16px;">تطويل واحد</button>
            <button onclick="addTatweel('{{ word.text }}', 2)" style="margin: 5px; padding: 8px 16px;">تطويل مضاعف</button>
            <button onclick="addTatweel('{{ word.text }}', 3)" style="margin: 5px; padding: 8px 16px;">تطويل ثلاثي</button>
        </div>
        <div id="tatweel-result" class="arabic-text" style="font-size: 28px; margin: 10px 0; min-height: 40px; border: 1px dashed #ccc; padding: 10px;">
            <!-- Tatweel result will appear here -->
        </div>
    </div>
</div>

<script>
function addTatweel(text, numTatweels) {
    const formData = new FormData();
    formData.append('text', text);
    formData.append('operation', 'add');
    formData.append('num_tatweels', numTatweels);
    
    fetch('/tatweel', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(result => {
        document.getElementById('tatweel-result').innerHTML = result;
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('tatweel-result').innerHTML = 'خطأ في إضافة التطويل';
    });
}
</script>

<style>
.word-details {
    direction: rtl;
    text-align: right;
}

.word-details h3, .word-details h4 {
    color: #333;
    margin: 15px 0 10px 0;
}

.word-info {
    margin: 20px 0;
}

.letters-container {
    direction: rtl;
    text-align: right;
}

.letter-item {
    text-align: center;
}

button {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: inherit;
}

button:hover {
    background: #0056b3;
}

#tatweel-result {
    background: #f8f9fa;
}
</style>
