import json
import os
import jsonschema
from typing import Dict, Any, Optional

from alifbaa.constants import JSON_SCHEMA_DIR


def load_schema(schema_name: str) -> Dict[str, Any]:
    schema_path = os.path.join(JSON_SCHEMA_DIR, schema_name)
    with open(schema_path, "r", encoding="utf-8") as f:
        return json.load(f)


def validate_data(data: Dict[str, Any], schema_name: str) -> Optional[str]:
    try:
        schema = load_schema(schema_name)
        jsonschema.validate(instance=data, schema=schema)
        return None
    except FileNotFoundError as e:
        return f"Schema file not found: {e}"
    except jsonschema.exceptions.ValidationError as e:
        return f"Validation error: {e.message}"
    except Exception as e:
        return f"Unexpected error during validation: {str(e)}"
