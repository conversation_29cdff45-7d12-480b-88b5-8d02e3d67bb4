from alifbaa.ir.verse import Verse
from alifbaa.ir.word import Word
from alifbaa.workers.text_operations import TextOperations
from alifbaa.repositories.parsers.pyarabic_parser import PyArabicParserRepository
import time


def test_parse_words():
    # Create a test verse
    verse = Verse(
        text="بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ",
        number=1,
        text_plain="بسم الله الرحمن الرحيم",
        translations={
            "en": "In the name of Allah, the Most Gracious, the Most Merciful"
        },
    )

    # Create the parser and text operations
    parser = PyArabicParserRepository()
    text_ops = TextOperations(parser)

    # Test the parse_words method
    start_time = time.time()
    words = text_ops.parse_words(verse)
    first_call_time = time.time() - start_time

    # Call it again to test caching
    start_time = time.time()
    words_cached = text_ops.parse_words(verse)
    cached_call_time = time.time() - start_time

    # Print the results
    print(f"Successfully parsed {len(words)} words:")
    for word in words:
        print(f"  - {word.text} (position: {word.position}, plain: {word.text_plain})")

    print(f"\nFirst call time: {first_call_time:.6f} seconds")
    print(f"Cached call time: {cached_call_time:.6f} seconds")

    # Avoid division by zero
    if cached_call_time > 0:
        print(f"Cache speedup: {first_call_time / cached_call_time:.2f}x faster")
    else:
        print("Cache speedup: Cached call was too fast to measure")

    # Verify that the cache is working by checking object identity
    print(f"\nCache working: {words is words_cached}")

    return True


if __name__ == "__main__":
    success = test_parse_words()
    print(f"\nTest {'passed' if success else 'failed'}")
