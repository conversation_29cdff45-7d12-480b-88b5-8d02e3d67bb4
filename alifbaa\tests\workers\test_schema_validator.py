import os
import json
import pytest
from unittest.mock import patch, mock_open

from alifbaa.workers.schema_validator import load_schema, validate_data


class TestSchemaValidator:
    def test_load_schema_returns_schema_dict(self, tmp_path):
        # Create a test schema file
        schema_content = {
            "$schema": "https://json-schema.org/draft/2020-12/schema",
            "type": "object",
            "properties": {"test": {"type": "string"}},
        }

        # Mock the open function to return our test schema
        mock_schema = json.dumps(schema_content)

        with patch("builtins.open", mock_open(read_data=mock_schema)):
            with patch("os.path.join", return_value="mock_path"):
                result = load_schema("test_schema.json")

                assert result == schema_content

    def test_validate_data_returns_none_for_valid_data(self):
        # Create a test schema
        schema = {
            "$schema": "https://json-schema.org/draft/2020-12/schema",
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
            "required": ["name"],
        }

        # Create valid test data
        valid_data = {"name": "Test User", "age": 30}

        # Mock load_schema to return our test schema
        with patch("alifbaa.workers.schema_validator.load_schema", return_value=schema):
            result = validate_data(valid_data, "test_schema.json")

            assert result is None

    def test_validate_data_returns_error_for_invalid_data(self):
        # Create a test schema
        schema = {
            "$schema": "https://json-schema.org/draft/2020-12/schema",
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
            "required": ["name"],
        }

        # Create invalid test data (missing required field)
        invalid_data = {"age": "thirty"}  # Wrong type and missing required field

        # Mock load_schema to return our test schema
        with patch("alifbaa.workers.schema_validator.load_schema", return_value=schema):
            result = validate_data(invalid_data, "test_schema.json")

            assert result is not None
            assert "Validation error" in result

    def test_validate_data_handles_missing_schema_file(self):
        # Mock load_schema to raise FileNotFoundError
        with patch(
            "alifbaa.workers.schema_validator.load_schema",
            side_effect=FileNotFoundError("File not found"),
        ):
            result = validate_data({"test": "data"}, "nonexistent_schema.json")

            assert result is not None
            assert "Schema file not found" in result
