from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass(frozen=True)
class WordPosition:
    """Precise positioning for words within the text."""

    surah: int
    ayah: int
    position: int

    def get_position_key(self) -> str:
        """Get unique position identifier."""
        return f"{self.surah}:{self.ayah}:{self.position}"


@dataclass(frozen=True)
class Word:
    text: str
    position: int
    text_plain: str
    # New fields for word-oriented architecture
    surah: Optional[int] = None
    ayah: Optional[int] = None
    position_in_verse: Optional[int] = None
    global_position: Optional[int] = None

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "Word":
        return cls(
            text=data["text"],
            position=data["position"],
            text_plain=data["text_plain"],
            surah=data.get("surah"),
            ayah=data.get("ayah"),
            position_in_verse=data.get("position_in_verse"),
            global_position=data.get("global_position"),
        )

    @classmethod
    def from_word_data(
        cls,
        word_id: str,
        text: str,
        text_plain: str,
        surah: int,
        ayah: int,
        position_in_verse: int,
    ) -> "Word":
        """Create Word from word data with positioning information."""
        return cls(
            text=text,
            position=position_in_verse,  # Keep for backward compatibility
            text_plain=text_plain,
            surah=surah,
            ayah=ayah,
            position_in_verse=position_in_verse,
            global_position=int(word_id),
        )

    def get_position_key(self) -> str:
        """Get unique position identifier."""
        if (
            self.surah is not None
            and self.ayah is not None
            and self.position_in_verse is not None
        ):
            return f"{self.surah}:{self.ayah}:{self.position_in_verse}"
        return f"unknown:{self.position}"

    def get_word_position(self) -> Optional[WordPosition]:
        """Get WordPosition object if positioning data is available."""
        if (
            self.surah is not None
            and self.ayah is not None
            and self.position_in_verse is not None
        ):
            return WordPosition(self.surah, self.ayah, self.position_in_verse)
        return None

    def to_json(self) -> Dict[str, Any]:
        result = {
            "text": self.text,
            "position": self.position,
            "text_plain": self.text_plain,
        }
        if self.surah is not None:
            result["surah"] = self.surah
        if self.ayah is not None:
            result["ayah"] = self.ayah
        if self.position_in_verse is not None:
            result["position_in_verse"] = self.position_in_verse
        if self.global_position is not None:
            result["global_position"] = self.global_position
        return result
