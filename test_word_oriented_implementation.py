"""
Basic integration test for word-oriented architecture components.
Tests the high priority implementations: Word IR, WordRepository, and VerseReconstitutionService.
"""

import os
import sys
from typing import List

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from alifbaa.ir.word import Word, WordPosition
from alifbaa.repositories.word_repository import WordRepository
from alifbaa.workers.verse_reconstitution import VerseReconstitutionService


def test_word_ir_enhancements():
    """Test the enhanced Word IR with positioning."""
    print("Testing Word IR enhancements...")

    # Test WordPosition
    pos = WordPosition(surah=1, ayah=1, position=0)
    assert pos.get_position_key() == "1:1:0"
    print("✓ WordPosition works correctly")

    # Test Word with positioning data
    word = Word.from_word_data(
        word_id="1", text="بِسْمِ", text_plain="بسم", surah=1, ayah=1, position_in_verse=0
    )

    assert word.surah == 1
    assert word.ayah == 1
    assert word.position_in_verse == 0
    assert word.global_position == 1
    assert word.get_position_key() == "1:1:0"

    word_pos = word.get_word_position()
    assert word_pos is not None
    assert word_pos.surah == 1
    assert word_pos.ayah == 1
    assert word_pos.position == 0

    print("✓ Enhanced Word IR works correctly")


def test_word_repository():
    """Test the WordRepository with real data."""
    print("Testing WordRepository...")

    # Initialize repository with quran data
    books_dir = "books/quran"
    if not os.path.exists(books_dir):
        print("⚠ Skipping WordRepository test - quran data not found")
        return

    word_repo = WordRepository(books_dir)

    # Test getting word by ID
    word = word_repo.get_word_by_id("1")
    if word:
        print(
            f"✓ Word 1: '{word.text}' at {word.surah}:{word.ayah}:{word.position_in_verse}"
        )
        assert word.surah == 1
        assert word.ayah == 1
        assert word.position_in_verse == 0
    else:
        print("⚠ Could not get word by ID")

    # Test getting word by position
    word_by_pos = word_repo.get_word_by_position(1, 1, 0)
    if word_by_pos:
        print(f"✓ Word at position 1:1:0: '{word_by_pos.text}'")
        assert word_by_pos.text == word.text if word else True
    else:
        print("⚠ Could not get word by position")

    # Test getting words for a verse
    verse_words = word_repo.get_words_for_verse(1, 1)
    if verse_words:
        print(f"✓ Found {len(verse_words)} words in verse 1:1")
        print(f"  First word: '{verse_words[0].text}'")
        print(f"  Last word: '{verse_words[-1].text}'")

        # Verify words are sorted by position
        for i in range(1, len(verse_words)):
            curr_pos = verse_words[i].position_in_verse or 0
            prev_pos = verse_words[i - 1].position_in_verse or 0
            assert curr_pos > prev_pos, "Words should be sorted by position"
    else:
        print("⚠ Could not get words for verse")

    # Test word count
    total_count = word_repo.get_word_count()
    verse_count = word_repo.get_word_count(surah=1, ayah=1)
    print(f"✓ Total words: {total_count}, Verse 1:1 words: {verse_count}")

    search_results = word_repo.search_words_by_text("بِسْمِ", surah=1)
    
    if search_results:
        print(f"✓ Found {len(search_results)} instances of 'بِسْمِ' in surah 1")
        for pos, word in search_results[:3]:
            print(f"  {pos.surah}:{pos.ayah}:{pos.position} - '{word.text}'")

    print("✓ WordRepository tests completed")
    assert verse_words is not None
    assert len(verse_words) > 0


def test_verse_reconstitution():
    """Test the VerseReconstitutionService."""
    print("Testing VerseReconstitutionService...")
    
    word_repo = WordRepository("books/quran")
    verse_words = word_repo.get_words_for_verse(1, 1)
    
    if not verse_words:
        print("⚠ Skipping VerseReconstitution test - no verse words found")
        return

    service = VerseReconstitutionService()

    verse = service.reconstitute_verse(verse_words)
    print(f"✓ Reconstituted verse {verse.number}")
    print(f"  Text: '{verse.text}'")
    print(f"  Text plain: '{verse.text_plain}'")
    print(f"  Word count: {len(verse.words)}")

    assert verse.number == verse_words[0].ayah
    assert len(verse.words) == len(verse_words)
    assert verse.text == " ".join(word.text for word in verse_words)
    assert verse.text_plain == " ".join(word.text_plain for word in verse_words)

    # Test verse validation
    is_valid = service.validate_word_sequence(verse_words)
    print(f"✓ Word sequence validation: {is_valid}")

    # Test caching
    cached_verse = service.reconstitute_verse_with_caching(verse_words)
    assert cached_verse.text == verse.text
    print("✓ Caching works correctly")

    # Test batch reconstitution
    words_by_verse = {(1, 1): verse_words}
    verses = service.reconstitute_verses_batch(words_by_verse)
    assert len(verses) == 1
    assert verses[0].text == verse.text
    print("✓ Batch reconstitution works correctly")

    print("✓ VerseReconstitutionService tests completed")


def run_integration_test():
    """Run the complete integration test."""
    print("=" * 60)
    print("WORD-ORIENTED ARCHITECTURE INTEGRATION TEST")
    print("=" * 60)

    try:
        # Test 1: Word IR enhancements
        test_word_ir_enhancements()

        # Test 2: WordRepository
        verse_words = test_word_repository()

        # Test 3: VerseReconstitutionService
        test_verse_reconstitution(verse_words)

        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED! High priority implementation is working.")
        print("=" * 60)

    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback

        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    success = run_integration_test()
    sys.exit(0 if success else 1)
