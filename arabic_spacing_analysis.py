#!/usr/bin/env python3
"""
Comprehensive analysis of Arabic text spacing and word processing behavior
"""

from alifbaa.ir.verse import Verse
from alifbaa.ir.word import Word
from alifbaa.workers.text_operations import TextOperations
from alifbaa.repositories.parsers.pyarabic_parser import PyArabicParserRepository
from pyarabic import araby
import json


def analyze_arabic_text_spacing():
    """Analyze how Arabic text with diacritics is currently handled"""
    
    print("=== ARABIC TEXT SPACING ANALYSIS ===\n")
    
    # Test cases with different Arabic text patterns
    test_cases = [
        {
            "name": "Al-Fatiha verse 1",
            "text": "بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ",
            "text_plain": "بسم الله الرحمن الرحيم"
        },
        {
            "name": "Simple word with diacritics",
            "text": "كِتَابٌ",
            "text_plain": "كتاب"
        },
        {
            "name": "Word with shadda and other diacritics",
            "text": "اللَّهُ",
            "text_plain": "الله"
        },
        {
            "name": "Mixed Arabic and spaces",
            "text": "قُلْ هُوَ اللَّهُ أَحَدٌ",
            "text_plain": "قل هو الله احد"
        }
    ]
    
    parser = PyArabicParserRepository()
    text_ops = TextOperations(parser)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"--- Test Case {i}: {test_case['name']} ---")
        
        # Create verse
        verse = Verse(
            text=test_case["text"],
            number=i,
            text_plain=test_case["text_plain"],
            translations={"en": f"Test case {i}"}
        )
        
        # Analyze character by character
        print(f"Original text: '{test_case['text']}'")
        print(f"Length: {len(test_case['text'])} characters")
        print("Character breakdown:")
        
        for j, char in enumerate(test_case["text"]):
            is_diacritic = araby.is_haraka(char) or araby.is_tashkeel(char)
            is_space = char == ' '
            is_letter = char in araby.LETTERS
            char_type = "SPACE" if is_space else "DIACRITIC" if is_diacritic else "LETTER" if is_letter else "OTHER"
            print(f"  {j:2d}: '{char}' ({ord(char):04x}) - {char_type}")
        
        # Parse words
        words = text_ops.parse_words(verse)
        print(f"\nParsed into {len(words)} words:")
        
        for word in words:
            print(f"  Word {word.position}: '{word.text}' -> plain: '{word.text_plain}'")
            
            # Analyze word character spacing
            print(f"    Length: {len(word.text)} chars")
            for k, char in enumerate(word.text):
                is_diacritic = araby.is_haraka(char) or araby.is_tashkeel(char)
                char_type = "DIACRITIC" if is_diacritic else "LETTER"
                print(f"      {k}: '{char}' - {char_type}")
        
        # Test tokenization details
        tokens = araby.tokenize_with_location(test_case["text"])
        print(f"\nPyArabic tokenization:")
        for token in tokens:
            print(f"  Token: '{token['token']}' at positions {token['start']}-{token['end']}")
        
        print("\n" + "="*60 + "\n")


def analyze_spacing_issues():
    """Analyze potential spacing issues in Arabic text processing"""
    
    print("=== SPACING ISSUES ANALYSIS ===\n")
    
    # Test specific spacing scenarios
    spacing_tests = [
        {
            "name": "Letter + Diacritic + Space + Letter",
            "text": "بِ اللَّهِ",
            "expected_issue": "Space after diacritic might cause rendering issues"
        },
        {
            "name": "Multiple diacritics on one letter",
            "text": "اللَّهُ",
            "expected_issue": "Multiple diacritics should stay with base letter"
        },
        {
            "name": "Tatweel with diacritics",
            "text": "بِـسْـمِ",
            "expected_issue": "Tatweel placement with diacritics"
        }
    ]
    
    parser = PyArabicParserRepository()
    text_ops = TextOperations(parser)
    
    for test in spacing_tests:
        print(f"--- {test['name']} ---")
        print(f"Text: '{test['text']}'")
        print(f"Expected issue: {test['expected_issue']}")
        
        # Character analysis
        print("Characters:")
        for i, char in enumerate(test["text"]):
            is_diacritic = araby.is_haraka(char) or araby.is_tashkeel(char)
            is_space = char == ' '
            is_tatweel = araby.is_tatweel(char)
            char_info = []
            if is_space:
                char_info.append("SPACE")
            if is_diacritic:
                char_info.append("DIACRITIC")
            if is_tatweel:
                char_info.append("TATWEEL")
            if not any([is_space, is_diacritic, is_tatweel]):
                char_info.append("LETTER")
            
            print(f"  {i}: '{char}' - {', '.join(char_info)}")
        
        # Test word parsing
        verse = Verse(text=test["text"], number=1, text_plain=araby.strip_tashkeel(test["text"]))
        words = text_ops.parse_words(verse)
        
        print(f"Parsed words: {len(words)}")
        for word in words:
            print(f"  '{word.text}' -> '{word.text_plain}'")
        
        print()


def test_tatweel_functionality():
    """Test the tatweel (kashida) functionality"""
    
    print("=== TATWEEL FUNCTIONALITY TEST ===\n")
    
    parser = PyArabicParserRepository()
    text_ops = TextOperations(parser)
    
    test_words = [
        "بسم",
        "بِسْمِ", 
        "الله",
        "اللَّهِ"
    ]
    
    for word_text in test_words:
        print(f"--- Testing word: '{word_text}' ---")
        
        # Create word object
        word = Word(
            text=word_text,
            position=0,
            text_plain=araby.strip_tashkeel(word_text)
        )
        
        try:
            # Test tatweel addition
            result = text_ops.add_tatweel(word, 1)
            print(f"With 1 tatweel: '{result}'")
            
            result2 = text_ops.add_tatweel(word, 2)
            print(f"With 2 tatweels: '{result2}'")
            
        except Exception as e:
            print(f"Error adding tatweel: {e}")
        
        print()


if __name__ == "__main__":
    analyze_arabic_text_spacing()
    analyze_spacing_issues()
    test_tatweel_functionality()
