# Python Custom Instructions

## Code Generation Instructions

- Use static typing throughout all code with appropriate type hints.
- Code should be self documenting, no need for inline comments nor docstrings unless absolutely necessary.
- Create Protocol classes instead of ABC for defining interfaces.
- Use dataclasses for data containers when appropriate.
- Use enums for representing fixed sets of values.
- Avoid literals and hardcoded values; use constants or configuration.
- Use absolute imports exclusively.
- Keep functions small and focused on a single responsibility.
- Follow SOLID principles, especially single responsibility and dependency inversion.
- Don't write SQL directly, use an ORM or query builder.
- Use meaningful variable and function names that explain their purpose.
- Return explicit values from functions rather than modifying parameters.
- Use exceptions for error handling rather than error codes.
- Extract complex conditions into well-named helper methods.
- Separate infrastructure concerns (API calls, database access) from business logic.
- Use dependency injection for external services.
- Design for testability with clear interfaces between components.
- Avoid global state and mutable shared state.
- Use type guards and isinstance() checks when dealing with union types.
- Prefer composition over inheritance.
- Use async/await for I/O bound operations in api calls.

## Test Generation Instructions

- Start with tests before implementing functionality (Test-Driven Development).
- Structure each test with clear setup, execution, and assertion phases separated by blank lines.
- Create focused unit tests that test a single behavior.
- Mock all external dependencies using unittest.mock.
- Use @patch decorators for mocking functions and classes.
- Mock all external API calls and repository access.
- Test all conditional branches and edge cases.
- Test exception paths using pytest.raises.
- Mock loggers and stdout/stderr when testing output.
- Use descriptive test names that explain what behavior is being tested.
- Place tests in a /tests directory following the same structure as the application code.
- Use pytest fixtures for common test setup.
- Use parameterized tests for testing multiple inputs.
- Test both positive and negative scenarios.
- Use pytest.mark for categorizing tests.
- Keep test assertions focused on behavior not implementation details.
- Write integration tests for critical paths.
- Use coverage tools to ensure comprehensive test coverage.

## Code Review Instructions

- Verify all code has appropriate type hints.
- Ensure Protocol interfaces are used instead of ABC.
- Check that dataclasses and enums are used appropriately.
- Verify no hardcoded values or literals exist in business logic.
- Confirm all imports are absolute.
- Verify all functions have a single responsibility.
- Check that dependencies are properly injected rather than created inside functions.
- Ensure tests exist for all conditionals and exception paths.
- Verify external calls are properly mocked in tests.
- Check that tests follow the setup/execution/assertion pattern.
- Ensure test names clearly describe the behavior being tested.
- Verify test coverage of edge cases.
- Check that functions return values rather than modifying parameters.
- Ensure error handling uses exceptions appropriately.
- Verify separation of infrastructure and business logic concerns.
- Check for proper abstraction of external services.
- Ensure clean separation of responsibilities between classes and modules.
