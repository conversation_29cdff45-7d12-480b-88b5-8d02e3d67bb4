from typing import Any, Dict
from unittest.mock import MagicMock, patch

import pytest
from flask import Flask
from alifbaa.ir.chapter import Chapter
from alifbaa.ir.verse import Verse
from alifbaa.ir.word import Word


@pytest.fixture
def client(app: Flask) -> Any:
    return app.test_client()


@pytest.fixture
def mock_quran_display_use_case() -> MagicMock:
    mock = MagicMock()
    mock.get_chapter_list.return_value = [(1, "الفاتحة"), (2, "البقرة")]
    return mock


@pytest.fixture
def mock_chapter() -> Chapter:
    verses = (
        Verse(
            text="بِسْمِ ٱللَّهِ ٱلرَّحْمَـٰنِ ٱلرَّحِيمِ",
            number=1,
            text_plain="بسم الله الرحمن الرحيم",
        ),
        Verse(
            text="ٱلْحَمْدُ لِلَّهِ رَبِّ ٱلْعَـٰلَمِينَ", number=2, text_plain="الحمد لله رب العالمين"
        ),
    )
    return Chapter(
        number=1, name="الفاتحة", translations={"en": "The Opening"}, verses=verses
    )


@pytest.fixture
def mock_verse() -> Verse:
    words = (
        Word(text="بِسْمِ", position=0, text_plain="بسم"),
        Word(text="ٱللَّهِ", position=1, text_plain="الله"),
        Word(text="ٱلرَّحْمَـٰنِ", position=2, text_plain="الرحمن"),
        Word(text="ٱلرَّحِيمِ", position=3, text_plain="الرحيم"),
    )
    return Verse(
        text="بِسْمِ ٱللَّهِ ٱلرَّحْمَـٰنِ ٱلرَّحِيمِ",
        number=1,
        text_plain="بسم الله الرحمن الرحيم",
        translations={
            "en": "In the name of Allah, the Most Gracious, the Most Merciful"
        },
        words=words,
    )


@pytest.fixture
def mock_chapters_info() -> Dict[int, Dict[str, str]]:
    return {
        1: {
            "arabic_name": "الفاتحة",
            "transcription_name": "Al-Fatiha",
            "translated_name": "The Opening",
        },
        2: {
            "arabic_name": "البقرة",
            "transcription_name": "Al-Baqarah",
            "translated_name": "The Cow",
        },
    }


def test_home_route_returns_200(client: Any) -> None:
    with patch("alifbaa.app.quran_display_use_case") as mock_use_case, patch(
        "alifbaa.app.quran_repository"
    ) as mock_repo:
        mock_use_case.get_chapter_list.return_value = [(1, "الفاتحة"), (2, "البقرة")]
        mock_repo._load_chapters.return_value = {
            1: {
                "arabic_name": "الفاتحة",
                "transcription_name": "Al-Fatiha",
                "translated_name": "The Opening",
            },
            2: {
                "arabic_name": "البقرة",
                "transcription_name": "Al-Baqarah",
                "translated_name": "The Cow",
            },
        }
        mock_repo.get_chapter_by_number.return_value = Chapter(
            number=1, name="الفاتحة", verses=()
        )

        response = client.get("/")

        assert response.status_code == 200
        assert b"Le Saint Coran" in response.data


def test_home_route_initializes_session_settings(client: Any) -> None:
    with patch("alifbaa.app.quran_display_use_case") as mock_use_case, patch(
        "alifbaa.app.quran_repository"
    ) as mock_repo:
        mock_use_case.get_chapter_list.return_value = [(1, "الفاتحة")]
        mock_repo._load_chapters.return_value = {
            1: {"arabic_name": "الفاتحة", "transcription_name": "Al-Fatiha"}
        }
        mock_repo.get_chapter_by_number.return_value = Chapter(
            number=1, name="الفاتحة", verses=()
        )

        with client.session_transaction() as sess:
            if "font" in sess:
                del sess["font"]
            if "font_size" in sess:
                del sess["font_size"]
            if "show_diacritics" in sess:
                del sess["show_diacritics"]

        client.get("/")

        with client.session_transaction() as sess:
            assert sess["font"] == "Amiri Quran"
            assert sess["font_size"] == 24
            assert sess["show_diacritics"] is True


def test_chapter_route_returns_200(client: Any) -> None:
    with patch("alifbaa.app.quran_repository") as mock_repo:
        verses = (
            Verse(
                text="بِسْمِ ٱللَّهِ ٱلرَّحْمَـٰنِ ٱلرَّحِيمِ",
                number=1,
                text_plain="بسم الله الرحمن الرحيم",
            ),
            Verse(
                text="ٱلْحَمْدُ لِلَّهِ رَبِّ ٱلْعَـٰلَمِينَ",
                number=2,
                text_plain="الحمد لله رب العالمين",
            ),
        )
        mock_repo.get_chapter_by_number.return_value = Chapter(
            number=1, name="الفاتحة", verses=verses
        )
        mock_repo._load_chapters.return_value = {
            1: {
                "arabic_name": "الفاتحة",
                "transcription_name": "Al-Fatiha",
                "translated_name": "The Opening",
            }
        }

        with client.session_transaction() as sess:
            sess["font"] = "Amiri Quran"
            sess["font_size"] = 24
            sess["show_diacritics"] = True

        response = client.get("/chapter/1")

        assert response.status_code == 200
        assert b"Al-Fatiha" in response.data


def test_chapter_route_redirects_if_chapter_not_found(client: Any) -> None:
    with patch("alifbaa.app.quran_repository") as mock_repo:
        mock_repo.get_chapter_by_number.return_value = None

        response = client.get("/chapter/999")

        assert response.status_code == 302
        assert response.location == "/"


def test_chapter_route_uses_diacritics_setting(client: Any) -> None:
    with patch("alifbaa.app.quran_repository") as mock_repo, patch(
        "alifbaa.app.quran_display_use_case"
    ) as mock_use_case:
        verses = (
            Verse(
                text="بِسْمِ ٱللَّهِ ٱلرَّحْمَـٰنِ ٱلرَّحِيمِ",
                number=1,
                text_plain="بسم الله الرحمن الرحيم",
            ),
        )
        mock_repo.get_chapter_by_number.return_value = Chapter(
            number=1, name="الفاتحة", verses=verses
        )
        mock_repo._load_chapters.return_value = {
            1: {
                "arabic_name": "الفاتحة",
                "transcription_name": "Al-Fatiha",
                "translated_name": "The Opening",
            }
        }
        mock_use_case.remove_diacritics.return_value = "بسم الله الرحمن الرحيم"

        with client.session_transaction() as sess:
            sess["font"] = "Amiri Quran"
            sess["font_size"] = 24
            sess["show_diacritics"] = False

        response = client.get("/chapter/1")

        assert response.status_code == 200
        mock_use_case.remove_diacritics.assert_called_once()


def test_verse_route_returns_200(client: Any) -> None:
    with patch("alifbaa.app.quran_display_use_case") as mock_use_case, patch(
        "alifbaa.app.quran_repository"
    ) as mock_repo:
        words = (
            Word(text="بِسْمِ", position=0, text_plain="بسم"),
            Word(text="ٱللَّهِ", position=1, text_plain="الله"),
        )
        verse = Verse(text="بِسْمِ ٱللَّهِ", number=1, text_plain="بسم الله", words=words)
        mock_use_case.get_verse.return_value = verse
        mock_repo.get_chapter_by_number.return_value = Chapter(
            number=1, name="الفاتحة", verses=()
        )
        mock_repo._load_chapters.return_value = {
            1: {
                "arabic_name": "الفاتحة",
                "transcription_name": "Al-Fatiha",
                "translated_name": "The Opening",
            }
        }

        with client.session_transaction() as sess:
            sess["font"] = "Amiri Quran"
            sess["font_size"] = 24
            sess["show_diacritics"] = True

        response = client.get("/verse/1/1")

        assert response.status_code == 200
        assert b"Verse 1" in response.data


def test_verse_route_redirects_if_verse_not_found(client: Any) -> None:
    with patch("alifbaa.app.quran_display_use_case") as mock_use_case:
        mock_use_case.get_verse.return_value = None

        with client.session_transaction() as sess:
            sess["font"] = "Amiri Quran"
            sess["font_size"] = 24
            sess["show_diacritics"] = True

        response = client.get("/verse/1/999")

        assert response.status_code == 302
        assert response.location == "/chapter/1"


def test_verse_route_uses_diacritics_setting(client: Any) -> None:
    with patch("alifbaa.app.quran_display_use_case") as mock_use_case, patch(
        "alifbaa.app.quran_repository"
    ) as mock_repo:
        words = (Word(text="بسم", position=0, text_plain="بسم"),)
        verse = Verse(text="بسم الله", number=1, text_plain="بسم الله", words=words)
        mock_use_case.get_verse_without_diacritics.return_value = verse
        mock_repo.get_chapter_by_number.return_value = Chapter(
            number=1, name="الفاتحة", verses=()
        )
        mock_repo._load_chapters.return_value = {
            1: {
                "arabic_name": "الفاتحة",
                "transcription_name": "Al-Fatiha",
                "translated_name": "The Opening",
            }
        }

        with client.session_transaction() as sess:
            sess["font"] = "Amiri Quran"
            sess["font_size"] = 24
            sess["show_diacritics"] = False

        response = client.get("/verse/1/1")

        assert response.status_code == 200
        mock_use_case.get_verse_without_diacritics.assert_called_once_with(1, 1)


def test_settings_route_updates_font(client: Any) -> None:
    response = client.post("/settings", data={"font": "Scheherazade New"})

    assert response.status_code == 302
    with client.session_transaction() as sess:
        assert sess["font"] == "Scheherazade New"


def test_settings_route_updates_font_size(client: Any) -> None:
    response = client.post("/settings", data={"font_size": "36"})

    assert response.status_code == 302
    with client.session_transaction() as sess:
        assert sess["font_size"] == 36


def test_settings_route_updates_diacritics(client: Any) -> None:
    response = client.post("/settings", data={"show_diacritics": "true"})

    assert response.status_code == 302
    with client.session_transaction() as sess:
        assert sess["show_diacritics"] is True


def test_settings_route_ignores_invalid_font(client: Any) -> None:
    with client.session_transaction() as sess:
        sess["font"] = "Amiri Quran"

    response = client.post("/settings", data={"font": "Invalid Font"})

    assert response.status_code == 302
    with client.session_transaction() as sess:
        assert sess["font"] == "Amiri Quran"


def test_settings_route_ignores_invalid_font_size(client: Any) -> None:
    with client.session_transaction() as sess:
        sess["font_size"] = 24

    response = client.post("/settings", data={"font_size": "invalid"})

    assert response.status_code == 302
    with client.session_transaction() as sess:
        assert sess["font_size"] == 24


def test_settings_route_ignores_out_of_range_font_size(client: Any) -> None:
    with client.session_transaction() as sess:
        sess["font_size"] = 24

    response = client.post("/settings", data={"font_size": "100"})

    assert response.status_code == 302
    with client.session_transaction() as sess:
        assert sess["font_size"] == 24
