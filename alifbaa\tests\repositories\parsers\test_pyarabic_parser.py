from alifbaa.repositories.parsers.pyarabic_parser import PyArabicParserRepository
from alifbaa.tests.factories import create_word, create_verse


class TestPyArabicParserRepository:
    def test_parse_words_returns_tuple_of_words(self) -> None:
        parser = PyArabicParserRepository()
        text = "العربية لغة جميلة"
        verse = create_verse(text=text)

        words = parser.parse_words(verse)

        expected_words = (
            create_word(text="العربية", position=0),
            create_word(text="لغة", position=1),
            create_word(text="جميلة", position=2),
        )
        assert words == expected_words

    def test_remove_tashkeel_returns_without_diacritics_text(self) -> None:
        parser = PyArabicParserRepository()
        text = "الْعَرَبِيّةُ"

        result = parser.remove_tashkeel(text)

        assert result == "العربية"
