from typing import Dict, Optional, Tuple

from alifbaa.ir.book import Book, BookMetadata
from alifbaa.ir.chapter import Chapter
from alifbaa.ir.verse import Verse
from alifbaa.ir.word import Word
from pyarabic import araby  # type: ignore


def create_word(
    text: str = "بِسْمِ",
    position: int = 0,
    text_plain: str = "",
) -> Word:
    if not text_plain:
        text_plain = araby.strip_tashkeel(text)
    return Word(text=text, position=position, text_plain=text_plain)


def create_verse(
    text: str = "بِسْمِ ٱللَّهِ ٱلرَّحْمَـٰنِ ٱلرَّحِيمِ",
    number: int = 1,
    text_plain: str = "",
    translations: Optional[Dict[str, str]] = None,
    words: Optional[Tuple[Word, ...]] = None,
) -> Verse:
    if words is None:
        words = tuple()
    if translations is None:
        translations = {}
    if not text_plain:
        text_plain = araby.strip_tashkeel(text)
    return Verse(
        text=text,
        number=number,
        text_plain=text_plain,
        translations=translations,
        words=words,
    )


def create_verse_without_diacritics(
    text: str = "بسم الله الرحمن الرحيم",
    number: int = 1,
    translations: Optional[Dict[str, str]] = None,
    words: Optional[Tuple[Word, ...]] = None,
) -> Verse:
    return create_verse(
        text=text,
        number=number,
        text_plain=text,
        translations=translations,
        words=words,
    )


def create_chapter(
    number: int = 1,
    name: str = "الفاتحة",
    translations: Optional[Dict[str, str]] = None,
    verses: Optional[Tuple[Verse, ...]] = None,
) -> Chapter:
    if verses is None:
        verses = (
            create_verse(
                text="بِسْمِ ٱللَّهِ ٱلرَّحْمَـٰنِ ٱلرَّحِيمِ",
                number=1,
            ),
            create_verse(
                text="ٱلْحَمْدُ لِلَّهِ رَبِّ ٱلْعَـٰلَمِينَ",
                number=2,
            ),
        )
    if translations is None:
        translations = {"en": "The Opening", "fr": "L'Ouverture"}
    return Chapter(number=number, name=name, translations=translations, verses=verses)


def create_book_metadata(
    id: str = "quran",
    title: str = "The Holy Quran",
    language: str = "ar",
    author: Optional[str] = None,
    book_type: Optional[str] = "religious",
    requires_word_parsing: bool = True,
    chapters_count: int = 1,
    verses_count: int = 2,
) -> BookMetadata:
    return BookMetadata(
        id=id,
        title=title,
        language=language,
        author=author,
        book_type=book_type,
        requires_word_parsing=requires_word_parsing,
        chapters_count=chapters_count,
        verses_count=verses_count,
    )


def create_book(
    metadata: Optional[BookMetadata] = None,
    chapters: Optional[Tuple[Chapter, ...]] = None,
) -> Book:
    if metadata is None:
        metadata = create_book_metadata()
    if chapters is None:
        chapters = (create_chapter(),)
    return Book(metadata=metadata, chapters=chapters)
