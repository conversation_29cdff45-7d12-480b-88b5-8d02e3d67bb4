from dataclasses import dataclass, field
from functools import cached_property
from types import MappingProxyType
from typing import Dict, Mapping, Tuple, Any

from alifbaa.ir.verse import Verse


@dataclass(frozen=True)
class Chapter:
    number: int
    name: str
    translations: Dict[str, str] = field(default_factory=dict)
    verses: Tuple[Verse, ...] = field(default_factory=tuple)

    @cached_property
    def verses_by_number(self) -> Mapping[int, Verse]:
        return MappingProxyType({v.number: v for v in self.verses})

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "Chapter":
        verses: Tuple[Verse, ...] = tuple()
        if "verses" in data:
            verses = tuple(Verse.from_json(v) for v in data["verses"])

        return cls(
            number=data["number"],
            name=data["name"],
            translations=data.get("translations", {}),
            verses=verses,
        )

    def to_json(self) -> Dict[str, Any]:
        return {
            "number": self.number,
            "name": self.name,
            "translations": self.translations,
            "verses": [verse.to_json() for verse in self.verses],
        }
