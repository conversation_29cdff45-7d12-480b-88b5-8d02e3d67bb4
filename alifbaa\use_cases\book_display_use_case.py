from typing import Dict, List, Optional, Any

from alifbaa.repositories.book_repository import BookRepository
from alifbaa.workers.text_operations import TextOperations
from alifbaa.ir.book import Book, BookMetadata
from alifbaa.ir.chapter import Chapter
from alifbaa.ir.verse import Verse


class BookDisplayUseCase:

    def __init__(self, repository: BookRepository, text_operations: TextOperations):
        self.repository = repository
        self.text_operations = text_operations

    def get_available_books(self) -> List[BookMetadata]:
        return self.repository.get_available_books()

    def get_book_metadata(self, book_id: str) -> Optional[BookMetadata]:
        return self.repository.get_book_metadata(book_id)

    def get_book(self, book_id: str) -> Optional[Book]:
        return self.repository.get_book(book_id)

    def get_chapter(self, book_id: str, chapter_num: int) -> Optional[Chapter]:
        return self.repository.get_chapter(book_id, chapter_num)

    def get_verse(
        self, book_id: str, chapter_num: int, verse_num: int
    ) -> Optional[Verse]:
        return self.repository.get_verse(book_id, chapter_num, verse_num)

    # For backward compatibility with API endpoints
    def get_book_json(self, book_id: str) -> Optional[Dict[str, Any]]:
        book = self.repository.get_book(book_id)
        return book.to_json() if book else None

    def get_chapter_json(
        self, book_id: str, chapter_num: int
    ) -> Optional[Dict[str, Any]]:
        chapter = self.repository.get_chapter(book_id, chapter_num)
        return chapter.to_json() if chapter else None

    def get_chapter_data(
        self, book_id: str, chapter_num: int, show_diacritics: bool = True
    ) -> Optional[Chapter]:
        chapter = self.repository.get_chapter(book_id, chapter_num)
        if not chapter:
            return None
        verses_with_words = []
        for verse in chapter.verses:
            words = self.text_operations.parse_words(verse)

            new_verse = Verse(
                text=verse.text,
                number=verse.number,
                text_plain=verse.text_plain,
                translations=verse.translations,
                words=words,
            )
            verses_with_words.append(new_verse)

        return Chapter(
            number=chapter.number,
            name=chapter.name,
            translations=chapter.translations,
            verses=tuple(verses_with_words),
        )

    def get_verse_data(
        self,
        book_id: str,
        chapter_num: int,
        verse_num: int,
        show_diacritics: bool = True,
    ) -> Optional[Dict[str, Any]]:
        chapter = self.repository.get_chapter(book_id, chapter_num)
        if not chapter:
            return None

        verse = chapter.verses_by_number.get(verse_num)
        if not verse:
            return None

        words = self.text_operations.parse_words(verse)

        verse = Verse(
            text=verse.text,
            number=verse.number,
            text_plain=verse.text_plain,
            translations=verse.translations,
            words=words,
        )

        return {"chapter": chapter, "verse": verse}
