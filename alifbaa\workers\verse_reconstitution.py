from typing import List, Dict, Tuple, Any, Optional

from alifbaa.ir.word import Word
from alifbaa.ir.verse import Verse


class VerseReconstitutionService:
    """Service for reconstituting verses from words."""

    def __init__(self):
        self._verse_cache: Dict[str, Verse] = {}

    def reconstitute_verse(
        self, words: List[Word], verse_metadata: Optional[Dict[str, Any]] = None
    ) -> Verse:
        """Create a verse from a list of words."""
        if not words:
            raise ValueError("Cannot reconstitute verse from empty word list")

        # Ensure words are sorted by position
        sorted_words = sorted(words, key=lambda w: w.position_in_verse or w.position)

        # Calculate verse text from words
        verse_text, verse_text_plain = self.calculate_verse_text(sorted_words)

        # Extract verse metadata from words if not provided
        if verse_metadata is None:
            verse_metadata = self._extract_verse_metadata(sorted_words)

        # Create verse with reconstituted data
        verse = Verse(
            number=verse_metadata.get("number", sorted_words[0].ayah),
            text=verse_text,
            text_plain=verse_text_plain,
            words=tuple(sorted_words),
        )

        return verse

    def reconstitute_verses_batch(
        self,
        words_by_verse: Dict[Tuple[int, int], List[Word]],
        metadata_by_verse: Optional[Dict[Tuple[int, int], Dict[str, Any]]] = None,
    ) -> List[Verse]:
        """Efficiently reconstitute multiple verses."""
        verses = []

        # Sort verses by surah and ayah
        sorted_verse_keys = sorted(words_by_verse.keys())

        for verse_key in sorted_verse_keys:
            words = words_by_verse[verse_key]
            verse_metadata = (
                metadata_by_verse.get(verse_key) if metadata_by_verse else None
            )

            try:
                verse = self.reconstitute_verse(words, verse_metadata)
                verses.append(verse)
            except ValueError as e:
                print(f"Warning: Could not reconstitute verse {verse_key}: {e}")
                continue

        return verses

    def calculate_verse_text(self, words: List[Word]) -> Tuple[str, str]:
        """Calculate verse text and text_plain from words."""
        if not words:
            return "", ""

        # Sort words by position to ensure correct order
        sorted_words = sorted(words, key=lambda w: w.position_in_verse or w.position)

        # Join words with spaces
        verse_text = " ".join(word.text for word in sorted_words)
        verse_text_plain = " ".join(word.text_plain for word in sorted_words)

        return verse_text, verse_text_plain

    def _extract_verse_metadata(self, words: List[Word]) -> Dict[str, Any]:
        """Extract verse metadata from words."""
        if not words:
            return {}

        first_word = words[0]
        return {
            "number": first_word.ayah,
            "surah": first_word.surah,
            "word_count": len(words),
        }

    def get_cached_verse(self, surah: int, ayah: int) -> Optional[Verse]:
        """Get cached verse if available."""
        cache_key = f"{surah}:{ayah}"
        return self._verse_cache.get(cache_key)

    def cache_verse(self, verse: Verse, surah: int, ayah: int) -> None:
        """Cache a reconstituted verse."""
        cache_key = f"{surah}:{ayah}"
        self._verse_cache[cache_key] = verse

    def clear_cache(self) -> None:
        """Clear the verse cache."""
        self._verse_cache.clear()

    def reconstitute_verse_with_caching(
        self, words: List[Word], verse_metadata: Optional[Dict[str, Any]] = None
    ) -> Verse:
        """Reconstitute verse with caching support."""
        if not words:
            raise ValueError("Cannot reconstitute verse from empty word list")

        # Check if we can determine surah and ayah for caching
        first_word = words[0]
        if first_word.surah is not None and first_word.ayah is not None:
            # Check cache first
            cached_verse = self.get_cached_verse(first_word.surah, first_word.ayah)
            if cached_verse is not None:
                return cached_verse

        # Reconstitute verse
        verse = self.reconstitute_verse(words, verse_metadata)

        # Cache the result if possible
        if first_word.surah is not None and first_word.ayah is not None:
            self.cache_verse(verse, first_word.surah, first_word.ayah)

        return verse

    def validate_word_sequence(self, words: List[Word]) -> bool:
        """Validate that words form a proper sequence for a verse."""
        if not words:
            return False

        # Check that all words belong to the same verse
        first_word = words[0]
        if first_word.surah is None or first_word.ayah is None:
            return False

        for word in words:
            if word.surah != first_word.surah or word.ayah != first_word.ayah:
                return False

        # Check for position gaps (words should be consecutive)
        sorted_words = sorted(words, key=lambda w: w.position_in_verse or w.position)
        expected_position = 0

        for word in sorted_words:
            actual_position = (
                word.position_in_verse
                if word.position_in_verse is not None
                else word.position
            )
            if actual_position != expected_position:
                return False
            expected_position += 1

        return True
