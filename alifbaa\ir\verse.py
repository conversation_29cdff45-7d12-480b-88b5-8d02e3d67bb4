from dataclasses import dataclass, field
from typing import Dict, Tuple, Any

from alifbaa.ir.word import Word


@dataclass(frozen=True)
class Verse:
    text: str
    number: int
    text_plain: str
    translations: Dict[str, str] = field(default_factory=dict)
    words: Tuple[Word, ...] = field(default_factory=tuple)

    def __hash__(self) -> int:
        """Custom hash implementation to make Verse hashable for lru_cache."""
        # Hash the immutable fields
        h = hash((self.text, self.number, self.text_plain))

        # Hash the translations dictionary by converting to a sorted tuple of items
        translations_tuple = tuple(sorted(self.translations.items()))
        h ^= hash(translations_tuple)

        # Hash the words tuple
        h ^= hash(self.words)

        return h

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "Verse":
        words: Tuple[Word, ...] = tuple()
        if "words" in data:
            words = tuple(Word.from_json(w) for w in data.get("words", []))

        return cls(
            text=data["text"],
            number=data["number"],
            text_plain=data["text_plain"],
            translations=data.get("translations", {}),
            words=words,
        )

    def to_json(self) -> Dict[str, Any]:
        result = {
            "text": self.text,
            "number": self.number,
            "text_plain": self.text_plain,
            "translations": self.translations,
        }

        if self.words:
            result["words"] = [word.to_json() for word in self.words]

        return result
