import configparser
import os
from typing import Dict, Optional

import platformdirs

APP_NAME = "alifbaa-books"


def get_config_file() -> str:
    config_dir = platformdirs.user_config_dir(APP_NAME)
    os.makedirs(config_dir, exist_ok=True)
    config_file = os.path.join(config_dir, "alifbaa.cfg")

    return os.environ.get("ALIFBAA_CONFIG", config_file)


def get_parser(config_file: Optional[str] = None) -> configparser.ConfigParser:
    if config_file is None:
        config_file = get_config_file()

    if not os.path.exists(config_file):
        raise FileNotFoundError(
            f"Configuration file not found: {config_file}. Please create a "
            "configuration file or set the ALIFBAA_CONFIG environment variable."
        )

    parser = configparser.ConfigParser()
    parser.read(config_file)
    return parser


def get_flask_config(config_file: Optional[str] = None) -> Dict[str, str]:
    parser = get_parser(config_file)

    if not parser.has_section("flask"):
        raise ValueError("Missing [flask] section in configuration file")

    flask_config = dict(parser.items("flask"))

    if "secret_key" not in flask_config:
        raise ValueError(
            "Missing 'secret_key' in [flask] section of configuration file"
        )

    return flask_config


def get_books_config(config_file: Optional[str] = None) -> Dict[str, str]:
    parser = get_parser(config_file)

    if not parser.has_section("books"):
        print(parser.sections())
        raise ValueError("Missing [books] section in configuration file")

    books_config = dict(parser.items("books"))

    required_keys = [
        "books_dir",
    ]
    missing_keys = [key for key in required_keys if key not in books_config]

    if missing_keys:
        raise ValueError(
            f"Missing required keys in [books] section: {', '.join(missing_keys)}"
        )

    return books_config
