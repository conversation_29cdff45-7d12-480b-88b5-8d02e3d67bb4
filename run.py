import os
import sys
from pathlib import Path

# Set the configuration file path
current_dir = Path(__file__).parent.absolute()
config_file = current_dir / "local_config.cfg"

# Set environment variable for the configuration file
os.environ["ALIFBAA_CONFIG"] = str(config_file)

# Import and run the application
from alifbaa.app import create_app

if __name__ == "__main__":
    app = create_app()
    app.run(debug=True)
