import json
from typing import Any

import pytest

from alifbaa.ir.book import Book, BookMetadata
from alifbaa.ir.chapter import Chapter
from alifbaa.ir.verse import Verse
from alifbaa.repositories.book_repository import BookRepository


class TestBookRepository:
    @pytest.fixture
    def mock_book_data(self) -> dict:
        return {
            "metadata": {
                "id": "test-book",
                "title": "Test Book",
                "language": "en",
                "requires_word_parsing": False,
                "chapters_count": 1,
                "verses_count": 2,
            },
            "chapters": [
                {
                    "number": 1,
                    "name": "Chapter 1",
                    "translations": {"en": "Chapter One", "fr": "Chapitre Un"},
                    "verses": [
                        {
                            "number": 1,
                            "text": "This is verse 1",
                            "translations": {"en": "Verse One", "fr": "Verset Un"},
                        },
                        {
                            "number": 2,
                            "text": "This is verse 2",
                            "translations": {"en": "Verse Two", "fr": "Verset Deux"},
                        },
                    ],
                }
            ],
        }

    @pytest.fixture
    def mock_book_dir(self, tmp_path: Any, mock_book_data: dict) -> str:
        book_dir = tmp_path / "books"
        book_dir.mkdir(exist_ok=True)

        book_file = book_dir / "test-book.json"
        with open(book_file, "w", encoding="utf-8") as f:
            json.dump(mock_book_data, f)

        return str(book_dir)

    def test_init_loads_book_metadata(self, mock_book_dir: str) -> None:
        repo = BookRepository(mock_book_dir)

        assert len(repo._book_metadata) == 1
        assert "test-book" in repo._book_metadata
        assert isinstance(repo._book_metadata["test-book"], BookMetadata)
        assert repo._book_metadata["test-book"].id == "test-book"
        assert repo._book_metadata["test-book"].title == "Test Book"

    def test_get_available_books_returns_list(self, mock_book_dir: str) -> None:
        repo = BookRepository(mock_book_dir)

        books = repo.get_available_books()

        assert len(books) == 1
        assert isinstance(books[0], BookMetadata)
        assert books[0].id == "test-book"

    def test_get_book_metadata_returns_metadata(self, mock_book_dir: str) -> None:
        repo = BookRepository(mock_book_dir)

        metadata = repo.get_book_metadata("test-book")

        assert metadata is not None
        assert isinstance(metadata, BookMetadata)
        assert metadata.id == "test-book"
        assert metadata.title == "Test Book"

    def test_get_book_metadata_returns_none_for_nonexistent_book(
        self, mock_book_dir: str
    ) -> None:
        repo = BookRepository(mock_book_dir)

        metadata = repo.get_book_metadata("nonexistent-book")

        assert metadata is None

    def test_get_book_returns_book(self, mock_book_dir: str) -> None:
        repo = BookRepository(mock_book_dir)

        book = repo.get_book("test-book")

        assert book is not None
        assert isinstance(book, Book)
        assert book.metadata.id == "test-book"
        assert len(book.chapters) == 1

    def test_get_book_returns_none_for_nonexistent_book(
        self, mock_book_dir: str
    ) -> None:
        repo = BookRepository(mock_book_dir)

        book = repo.get_book("nonexistent-book")

        assert book is None

    def test_get_chapter_returns_chapter(self, mock_book_dir: str) -> None:
        repo = BookRepository(mock_book_dir)

        chapter = repo.get_chapter("test-book", 1)

        assert chapter is not None
        assert isinstance(chapter, Chapter)
        assert chapter.number == 1
        assert chapter.name == "Chapter 1"
        assert len(chapter.verses) == 2

    def test_get_chapter_returns_none_for_nonexistent_chapter(
        self, mock_book_dir: str
    ) -> None:
        repo = BookRepository(mock_book_dir)

        chapter = repo.get_chapter("test-book", 999)

        assert chapter is None

    def test_get_verse_returns_verse(self, mock_book_dir: str) -> None:
        repo = BookRepository(mock_book_dir)

        verse = repo.get_verse("test-book", 1, 1)

        assert verse is not None
        assert isinstance(verse, Verse)
        assert verse.number == 1
        assert verse.text == "This is verse 1"

    def test_get_verse_returns_none_for_nonexistent_verse(
        self, mock_book_dir: str
    ) -> None:
        repo = BookRepository(mock_book_dir)

        verse = repo.get_verse("test-book", 1, 999)

        assert verse is None

    def test_get_verse_returns_none_for_nonexistent_chapter(
        self, mock_book_dir: str
    ) -> None:
        repo = BookRepository(mock_book_dir)

        verse = repo.get_verse("test-book", 999, 1)

        assert verse is None
