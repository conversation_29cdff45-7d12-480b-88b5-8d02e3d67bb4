from typing import Any
from unittest.mock import patch

import pytest

from alifbaa.app import create_app


@pytest.fixture
def mock_book_dir(tmp_path: Any) -> str:
    book_dir = tmp_path / "books"
    book_dir.mkdir(exist_ok=True)
    return str(book_dir)


@pytest.fixture
def fake_config_file(tmp_path: Any, mock_book_dir: str) -> str:
    config_file = tmp_path / "config.cfg"
    config_content = [
        "[flask]",
        "secret_key = test_secret_key",
        "",
        "[books]",
        f"books_dir = {mock_book_dir}",
    ]
    config_file.write_text("\n".join(config_content), encoding="utf-8")
    return str(config_file)


@pytest.fixture
def app(fake_config_file: Any) -> Any:
    with patch.dict("alifbaa.config.os.environ", {"ALIFBAA_CONFIG": fake_config_file}):
        app = create_app()
        app.config["TESTING"] = True
        yield app


@pytest.fixture
def client(app: Any) -> Any:
    return app.test_client()
