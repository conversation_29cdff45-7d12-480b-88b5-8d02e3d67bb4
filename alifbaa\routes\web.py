from flask import (
    Blueprint,
    render_template,
    request,
    redirect,
    url_for,
    current_app,
    make_response,
    Response,
)

from typing import Dict, Any, Union
import json
from functools import lru_cache
from alifbaa.constants import AVAILABLE_FONTS

from alifbaa.workers.book_data_fetcher import (
    get_available_books,
    get_book_metadata,
    get_book,
    get_chapter_data,
    get_verse_data,
)
from alifbaa.workers.panel_settings_worker import (
    update_settings,
    get_or_set_default_settings,
)
from alifbaa.workers.text_operations import TextOperations
from alifbaa.repositories.parsers.pyarabic_parser import PyArabicParserRepository
from alifbaa.ir.word import Word

web_bp = Blueprint("web", __name__)


@lru_cache(maxsize=1)
def get_text_operations() -> TextOperations:
    """Shared factory for TextOperations to avoid duplication."""
    parser = PyArabicParserRepository()
    return TextOperations(parser)


@web_bp.route("/")
def home() -> Union[str, Response]:
    # For the first iteration, redirect directly to the Quran home page
    return redirect(url_for("web.quran_home"))  # type: ignore

    # This code will be used later when we implement the full app
    # settings = get_or_set_default_settings()
    # books = get_available_books(current_app.config["ALIFBAA_CONFIG"])
    # return render_template(
    #     "pages/books/home.jinja",
    #     books=books,
    #     fonts=AVAILABLE_FONTS,
    #     current_font=settings["font"],
    #     current_font_size=settings["font_size"],
    #     show_diacritics=settings["show_diacritics"],
    #     is_quran=False,
    # )


@web_bp.route("/quran")
def quran_home() -> Union[str, Response]:
    settings = get_or_set_default_settings()

    book = get_book("quran", current_app.config["ALIFBAA_CONFIG"])
    if not book:
        return redirect(url_for("web.home"))  # type: ignore

    return render_template(
        "pages/quran/home.jinja",
        book=book,
        book_id="quran",
        fonts=AVAILABLE_FONTS,
        current_font=settings["font"],
        current_font_size=settings["font_size"],
        show_diacritics=settings["show_diacritics"],
        page_title="Le Saint Coran",
        is_quran=True,
    )


@web_bp.route("/book/<book_id>")
def book_details(book_id: str) -> Union[str, Response]:
    settings = get_or_set_default_settings()

    # Redirect Quran to its dedicated app
    if book_id == "quran":
        return redirect(url_for("web.quran_home"))  # type: ignore

    book = get_book(book_id, current_app.config["ALIFBAA_CONFIG"])
    if not book:
        return redirect(url_for("web.home"))  # type: ignore

    return render_template(
        "pages/books/book_details.jinja",
        book=book,
        book_id=book_id,
        fonts=AVAILABLE_FONTS,
        current_font=settings["font"],
        current_font_size=settings["font_size"],
        show_diacritics=settings["show_diacritics"],
        is_quran=False,
    )


@web_bp.route("/book/<book_id>/chapter/<int:chapter_num>")
def chapter(book_id: str, chapter_num: int) -> Union[str, Response]:
    settings = get_or_set_default_settings()

    # Get the book for metadata
    book = get_book(book_id, current_app.config["ALIFBAA_CONFIG"])
    if not book:
        return redirect(url_for("web.home"))  # type: ignore

    chapter = get_chapter_data(
        book_id,
        chapter_num,
        current_app.config["ALIFBAA_CONFIG"],
        settings["show_diacritics"],
    )
    if not chapter:
        return redirect(url_for("web.book_details", book_id=book_id))  # type: ignore

    # Use different template based on book type
    is_quran = book_id == "quran"
    template_path = (
        "pages/quran/chapter.jinja" if is_quran else "pages/books/chapter.jinja"
    )

    return render_template(
        template_path,
        book_id=book_id,
        book=book,
        chapter=chapter,
        fonts=AVAILABLE_FONTS,
        current_font=settings["font"],
        current_font_size=settings["font_size"],
        show_diacritics=settings["show_diacritics"],
        is_quran=is_quran,
    )


@web_bp.route("/book/<book_id>/chapter/<int:chapter_num>/verse/<int:verse_num>")
def verse(book_id: str, chapter_num: int, verse_num: int) -> Union[str, Response]:
    settings = get_or_set_default_settings()

    # Get the book for metadata
    book = get_book(book_id, current_app.config["ALIFBAA_CONFIG"])
    if not book:
        return redirect(url_for("web.home"))  # type: ignore

    verse_data = get_verse_data(
        book_id,
        chapter_num,
        verse_num,
        current_app.config["ALIFBAA_CONFIG"],
        settings["show_diacritics"],
    )
    if not verse_data:
        return redirect(
            url_for("web.chapter", book_id=book_id, chapter_num=chapter_num)
        )  # type: ignore

    # Use different template based on book type
    is_quran = book_id == "quran"
    template_path = "pages/quran/verse.jinja" if is_quran else "pages/books/verse.jinja"

    return render_template(
        template_path,
        book_id=book_id,
        book=book,
        chapter=verse_data["chapter"],
        verse=verse_data["verse"],
        fonts=AVAILABLE_FONTS,
        current_font=settings["font"],
        current_font_size=settings["font_size"],
        show_diacritics=settings["show_diacritics"],
        is_quran=is_quran,
    )


@web_bp.route("/settings", methods=["POST"])
def settings() -> Union[str, Response]:
    settings_data: Dict[str, Any] = {}

    if "font" in request.form:
        settings_data["font"] = request.form["font"]

    if "font_size" in request.form:
        settings_data["font_size"] = request.form["font_size"]

    if "show_diacritics" in request.form:
        settings_data["show_diacritics"] = request.form["show_diacritics"]
    else:
        settings_data["show_diacritics"] = False

    update_settings(settings_data)

    # Check if this is an htmx request
    if request.headers.get("HX-Request"):
        settings = get_or_set_default_settings()
        return render_template(
            "components/settings_panel.jinja",
            fonts=AVAILABLE_FONTS,
            current_font=settings["font"],
            current_font_size=settings["font_size"],
            show_diacritics=settings["show_diacritics"],
        )

    return redirect(request.referrer or url_for("web.home"))  # type: ignore


@web_bp.route("/books/<book_id>/chapters/<int:chapter_num>/content", methods=["GET"])
def chapter_content(book_id: str, chapter_num: int) -> Dict[str, Any]:
    # Return a message instead of rendering the template
    message = (
        f"Route deprecated. Use main chapter route for {book_id} ch.{chapter_num}."
    )
    return {"message": message}


@web_bp.route(
    "/books/<book_id>/chapters/<int:chapter_num>/verses/<int:verse_num>/content",
    methods=["GET"],
)
def verse_content(
    book_id: str, chapter_num: int, verse_num: int
) -> Union[str, Response]:
    settings = get_or_set_default_settings()

    book = get_book(book_id, current_app.config["ALIFBAA_CONFIG"])

    verse_data = get_verse_data(
        book_id,
        chapter_num,
        verse_num,
        current_app.config["ALIFBAA_CONFIG"],
        settings["show_diacritics"],
    )

    if not verse_data:
        return make_response("Verse not found", 404)

    return render_template(
        "components/verse_content.jinja",
        book_id=book_id,
        book=book,
        chapter=verse_data["chapter"],
        verse=verse_data["verse"],
        show_diacritics=settings["show_diacritics"],
    )


@web_bp.route("/word-details", methods=["POST"])
def word_details() -> Union[str, Response]:
    word_text = request.form.get("word_text")
    word_text_plain = request.form.get("word_text_plain")
    verse_num = request.form.get("verse_num")
    position = request.form.get("position")

    if not word_text:
        return make_response("Word text is required", 400)

    # Ensure text_plain is provided
    if not word_text_plain:
        # Use shared TextOperations instance to remove diacritics
        text_operations = get_text_operations()
        word_text_plain = text_operations.remove_diacritics(word_text)

    # Create a Word object with both diacritics and plain versions
    word = Word(
        text=word_text,
        position=int(position) if position else 0,
        text_plain=word_text_plain,
    )

    # Break down the word into individual letters
    letters = list(word_text)
    letters_plain = list(word_text_plain)

    return render_template(
        "components/word_details.jinja",
        word=word,
        verse_num=verse_num,
        letters=letters,
        letters_plain=letters_plain,
    )


@web_bp.route("/tatweel", methods=["POST"])
def tatweel_operations() -> Union[str, Response]:
    text = request.form.get("text")
    operation = request.form.get("operation")
    num_tatweels = int(request.form.get("num_tatweels", 1))

    if not text or not operation:
        return make_response("Missing required parameters", 400)

    text_operations = get_text_operations()

    if operation == "add":
        # Create a plain text version by removing diacritics
        text_plain = text_operations.remove_diacritics(text)
        word = Word(text=text, position=0, text_plain=text_plain)
        result = text_operations.add_tatweel(word, num_tatweels)
        return result
    elif operation == "remove":
        result = text.replace("ـ", "")
        return result
    else:
        return make_response("Invalid operation", 400)


@web_bp.route("/copy-to-clipboard", methods=["POST"])
def copy_to_clipboard() -> Union[str, Response]:
    text = request.form.get("text")

    if not text:
        return make_response("Missing text parameter", 400)

    response = make_response("")
    response.headers["HX-Trigger"] = json.dumps(
        {
            "showToast": {
                "message": "Copied to clipboard",
                "type": "success",
                "text": text,
            }
        }
    )

    return response


@web_bp.route("/toggle-diacritics", methods=["POST"])
def toggle_diacritics() -> Dict[str, Any]:
    """Toggle diacritics display for a chapter."""
    # Get parameters from request
    show_diacritics_str = request.form.get("show_diacritics")
    show_diacritics = show_diacritics_str == "true" if show_diacritics_str else False

    # Get font settings from request if available
    font_family = request.form.get("font_family")
    font_size = request.form.get("font_size")

    # Update settings
    settings_data = {"show_diacritics": show_diacritics}

    # Add font settings if provided
    if font_family and font_family in AVAILABLE_FONTS:
        settings_data["font"] = font_family  # type: ignore

    if font_size:
        try:
            font_size_int = int(font_size)
            if 12 <= font_size_int <= 48:
                settings_data["font_size"] = font_size_int  # type: ignore
        except (ValueError, TypeError):
            pass

    update_settings(settings_data)

    # For client-side implementation, we don't need to return the full HTML
    # Just return a success message
    return {"success": True, "settings_updated": settings_data}
