{% extends "base.jinja" %}

{% block title %}{{ page_title }} - الصفحة الرئيسية{% endblock %}

{% block content %}
<div class="quran-home">
    <div class="header">
        <h1>{{ page_title }}</h1>
        <p>اختر السورة للقراءة</p>
    </div>
    
    <div id="settings-panel">
        {% include "components/settings_panel.jinja" %}
    </div>
    
    <div class="chapters-grid">
        {% if book and book.chapters %}
            {% for chapter in book.chapters %}
            <div class="chapter-card">
                <a href="/book/{{ book_id }}/chapter/{{ chapter.number }}">
                    <div class="chapter-number">{{ chapter.number }}</div>
                    <div class="chapter-name arabic-text">{{ chapter.name }}</div>
                    {% if chapter.translations and chapter.translations.get('fr') %}
                    <div class="chapter-translation">{{ chapter.translations.fr }}</div>
                    {% endif %}
                    <div class="chapter-info">
                        {% if chapter.verses %}
                        <span>{{ chapter.verses|length }} آية</span>
                        {% endif %}
                    </div>
                </a>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-chapters">
                <p>لا توجد سور متاحة</p>
            </div>
        {% endif %}
    </div>
</div>

<style>
.quran-home {
    direction: rtl;
}

.header {
    text-align: center;
    margin: 30px 0;
}

.header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.chapters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.chapter-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.chapter-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.chapter-card a {
    display: block;
    padding: 20px;
    text-decoration: none;
    color: inherit;
}

.chapter-number {
    background: #007bff;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 15px;
}

.chapter-name {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
    color: #2c3e50;
}

.chapter-translation {
    font-size: 16px;
    color: #666;
    margin: 8px 0;
    direction: ltr;
    text-align: left;
}

.chapter-info {
    font-size: 14px;
    color: #888;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}

.no-chapters {
    text-align: center;
    padding: 40px;
    color: #666;
}
</style>
{% endblock %}
