# Quran Styler

A web application for reading and styling the Quran.

## Features

- Browse chapters and verses of the Quran
- Customize font (<PERSON><PERSON>, Scheherazade New, etc.)
- Adjust text size
- Toggle diacritics on/off

## Installation

1. Clone the repository:
```
git clone https://github.com/yourusername/quran-styler.git
cd quran-styler
```

2. Install the required packages:
```
pip install -r requirements.txt
```

## Running the Application

Run the application with:
```
python run.py
```

The application will be available at http://127.0.0.1:5000/

## Project Structure

- `alifbaa/` - Main application package
  - `app.py` - Flask application
  - `templates/` - Jinja2 templates
  - `static/` - Static files (CSS, JS)
  - `repositories/` - Data access layer
  - `use_cases/` - Business logic
  - `ir/` - Information representation (data models)
- `quran-uthmani.txt` - Quran text with diacritics
- `quran-simple-clean.txt` - Quran text without diacritics
- `quran-chapters.txt` - Chapter information

## License

This project is licensed under the MIT License - see the LICENSE file for details.
