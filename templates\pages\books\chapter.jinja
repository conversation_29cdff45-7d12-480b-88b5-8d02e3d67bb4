{% extends "base.jinja" %}

{% block title %}{{ chapter.name }} - الفصل {{ chapter.number }}{% endblock %}

{% block content %}
<div class="book-chapter">
    <div class="navigation">
        <a href="/book/{{ book_id }}">العودة إلى الكتاب</a>
        {% if chapter.number > 1 %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number - 1 }}">الفصل السابق</a>
        {% endif %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number + 1 }}">الفصل التالي</a>
    </div>
    
    <div class="chapter-header">
        <div class="chapter-number">{{ chapter.number }}</div>
        <h1 class="chapter-name arabic-text">{{ chapter.name }}</h1>
        {% if chapter.translations %}
            {% for lang, translation in chapter.translations.items() %}
            <div class="chapter-translation">{{ translation }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div id="settings-panel">
        {% include "components/settings_panel.jinja" %}
    </div>
    
    <div class="verses-container">
        {% if chapter.verses %}
            {% for verse in chapter.verses %}
            <div class="verse">
                {% include "components/verse_content.jinja" %}
            </div>
            {% endfor %}
        {% else %}
            <div class="no-verses">
                <p>لا توجد آيات متاحة</p>
            </div>
        {% endif %}
    </div>
    
    <div class="navigation">
        <a href="/book/{{ book_id }}">العودة إلى الكتاب</a>
        {% if chapter.number > 1 %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number - 1 }}">الفصل السابق</a>
        {% endif %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number + 1 }}">الفصل التالي</a>
    </div>
</div>

<style>
.book-chapter {
    direction: rtl;
}

.chapter-header {
    text-align: center;
    margin: 30px 0;
    padding: 30px;
    background: #28a745;
    color: white;
    border-radius: 12px;
}

.chapter-number {
    background: rgba(255,255,255,0.2);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
    margin-bottom: 15px;
}

.chapter-name {
    font-size: 32px;
    margin: 15px 0;
}

.chapter-translation {
    font-size: 16px;
    opacity: 0.9;
    margin: 8px 0;
}

.verses-container {
    margin: 30px 0;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.verse {
    margin: 20px 0;
}

.no-verses {
    text-align: center;
    padding: 40px;
    color: #666;
}
</style>
{% endblock %}
