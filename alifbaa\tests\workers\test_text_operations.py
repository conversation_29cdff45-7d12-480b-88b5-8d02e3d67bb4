import pytest
from alifbaa.workers.text_operations import TextOperations
from alifbaa.repositories.parsers.pyarabic_parser import PyArabicParserRepository
from alifbaa.ir.word import Word


class TestTextOperations:
    def test_remove_diacritics_removes_all_diacritics(self):
        parser = PyArabicParserRepository()
        text_operations = TextOperations(parser)
        text = "الْعَرَبِيّةُ"
        result = text_operations.remove_diacritics(text)
        assert result == "العربية"

    @pytest.mark.parametrize(
        "input_text,expected_output",
        [
            ("بسم", "بـسـم"),
            ("بِسْمِ", "بِـسْـمِ"),
            ("الله", "الـلـه"),
            ("ٱل", "ٱل"),
            ("ٱلرَّحْمٰنِ", "ٱلـرَّحْـمٰـنِ"),
            ("ٱلرَّحِيمِ", "ٱلـرَّحِـيـمِ"),
            ("", ""),
            ("ب", "ب"),
            ("بـسـم", "بـسـم"),
            ("بسم ABC", "بـسـم ABC"),
        ],
    )
    def test_add_tatweel(self, input_text, expected_output):
        parser = PyArabicParserRepository()
        text_operations = TextOperations(parser)
        word = Word(
            text=input_text,
            position=0,
            text_plain=input_text.replace("ِ", "")
            .replace("ْ", "")
            .replace("َ", "")
            .replace("ّ", ""),
        )
        result = text_operations.add_tatweel(word)
        assert result == expected_output

    @pytest.mark.parametrize(
        "input_text,num_tatweels,expected_output",
        [
            ("بسم", 1, "بـسـم"),
            ("بسم", 2, "بــســم"),
            ("بسم", 3, "بـــســـم"),
            ("بِسْمِ", 2, "بِــسْــمِ"),
            ("بسم", 0, "بسم"),
        ],
    )
    def test_add_multiple_tatweels(self, input_text, num_tatweels, expected_output):
        parser = PyArabicParserRepository()
        text_operations = TextOperations(parser)
        word = Word(
            text=input_text,
            position=0,
            text_plain=input_text.replace("ِ", "")
            .replace("ْ", "")
            .replace("َ", "")
            .replace("ّ", ""),
        )
        result = text_operations.add_tatweel(word, num_tatweels)
        assert result == expected_output
