<div class="verse-content">
    <div class="verse-header">
        <span class="verse-number">{{ verse.number }}</span>
    </div>
    
    <div class="verse-text arabic-text">
        {% if verse.words %}
            {% for word in verse.words %}
                <span class="word" 
                      onclick="showWordDetails('{{ word.text }}', '{{ word.text_plain }}', '{{ verse.number }}', '{{ word.position }}')"
                      title="انقر لعرض تفاصيل الكلمة">
                    {% if show_diacritics %}{{ word.text }}{% else %}{{ word.text_plain }}{% endif %}
                </span>
            {% endfor %}
        {% else %}
            <!-- Fallback if no words are parsed -->
            <span class="verse-text-fallback">
                {% if show_diacritics %}{{ verse.text }}{% else %}{{ verse.text_plain }}{% endif %}
            </span>
        {% endif %}
    </div>
    
    {% if verse.translations %}
    <div class="verse-translations">
        {% for lang, translation in verse.translations.items() %}
        <div class="translation translation-{{ lang }}">
            <span class="translation-text">{{ translation }}</span>
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>

<style>
.verse-content {
    margin: 15px 0;
    padding: 15px;
    border-right: 3px solid #007bff;
    background: #fafafa;
    border-radius: 4px;
}

.verse-header {
    margin-bottom: 10px;
}

.verse-number {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 4px 8px;
    border-radius: 50%;
    font-size: 14px;
    font-weight: bold;
    min-width: 20px;
    text-align: center;
}

.verse-text {
    margin: 10px 0;
    line-height: 2;
}

.word {
    display: inline-block;
    margin: 2px;
    padding: 2px 4px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.word:hover {
    background-color: #e3f2fd;
    cursor: pointer;
}

.verse-text-fallback {
    display: block;
    padding: 5px 0;
}

.verse-translations {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.translation {
    margin: 5px 0;
    font-size: 16px;
    color: #666;
    direction: ltr;
    text-align: left;
}

.translation-text {
    font-style: italic;
}
</style>
