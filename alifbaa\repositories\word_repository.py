import json
import os
from typing import Dict, <PERSON>, Optional, Tuple
from functools import lru_cache
from pyarabic import araby  # type: ignore

from alifbaa.ir.word import Word, WordPosition


class WordRepository:
    def __init__(self, data_dir: str):
        self.data_dir = data_dir
        self._word_positions: Optional[Dict[str, Dict]] = None
        self._word_texts: Optional[Dict[str, str]] = None

    def _load_word_data(self) -> None:
        if self._word_positions is None:
            word_json_path = os.path.join(self.data_dir, "word.json")
            if os.path.exists(word_json_path):
                with open(word_json_path, "r", encoding="utf-8") as f:
                    self._word_positions = json.load(f)
            else:
                self._word_positions = {}

        if self._word_texts is None:
            word_text_json_path = os.path.join(self.data_dir, "word-text.json")
            if os.path.exists(word_text_json_path):
                with open(word_text_json_path, "r", encoding="utf-8") as f:
                    self._word_texts = json.load(f)
            else:
                self._word_texts = {}

    def _get_text_plain(self, text: str) -> str:
        try:
            return araby.strip_tashkeel(text)
        except Exception:
            return text

    def get_word_by_id(self, word_id: str) -> Optional[Word]:
        self._load_word_data()

        if word_id not in self._word_positions or word_id not in self._word_texts:
            return None

        position_data = self._word_positions[word_id]
        text = self._word_texts[word_id]
        text_plain = self._get_text_plain(text)

        return Word.from_word_data(
            word_id=word_id,
            text=text,
            text_plain=text_plain,
            surah=position_data["surah"],
            ayah=position_data["ayah"],
            position_in_verse=position_data["position"],
        )

    def get_word_by_position(
        self, surah: int, ayah: int, position: int
    ) -> Optional[Word]:
        self._load_word_data()

        for word_id, pos_data in self._word_positions.items():
            if (
                pos_data["surah"] == surah
                and pos_data["ayah"] == ayah
                and pos_data["position"] == position
            ):
                return self.get_word_by_id(word_id)
        return None

    def get_words_for_verse(self, surah: int, ayah: int) -> List[Word]:
        self._load_word_data()

        verse_words = []
        for word_id, pos_data in self._word_positions.items():
            if pos_data["surah"] == surah and pos_data["ayah"] == ayah:
                word = self.get_word_by_id(word_id)
                if word:
                    verse_words.append(word)

        verse_words.sort(key=lambda w: w.position_in_verse or 0)
        return verse_words

    def get_words_range(
        self, start_pos: WordPosition, end_pos: WordPosition
    ) -> List[Word]:
        self._load_word_data()

        words_in_range = []
        for word_id, pos_data in self._word_positions.items():
            surah = pos_data["surah"]
            ayah = pos_data["ayah"]
            position = pos_data["position"]

            if self._is_position_in_range(surah, ayah, position, start_pos, end_pos):
                word = self.get_word_by_id(word_id)
                if word:
                    words_in_range.append(word)

        words_in_range.sort(key=lambda w: w.global_position or 0)
        return words_in_range

    def _is_position_in_range(
        self,
        surah: int,
        ayah: int,
        position: int,
        start_pos: WordPosition,
        end_pos: WordPosition,
    ) -> bool:
        if surah < start_pos.surah:
            return False
        if surah == start_pos.surah:
            if ayah < start_pos.ayah:
                return False
            if ayah == start_pos.ayah and position < start_pos.position:
                return False

        if surah > end_pos.surah:
            return False
        if surah == end_pos.surah:
            if ayah > end_pos.ayah:
                return False
            if ayah == end_pos.ayah and position > end_pos.position:
                return False

        return True

    def search_words_by_text(
        self, text: str, surah: Optional[int] = None
    ) -> List[Tuple[WordPosition, Word]]:
        """Search for words by text content."""
        self._load_word_data()

        results = []
        search_text = text.strip()

        for word_id, word_text in self._word_texts.items():
            if search_text in word_text:
                if word_id in self._word_positions:
                    pos_data = self._word_positions[word_id]
                    word_surah = pos_data["surah"]

                    if surah is not None and word_surah != surah:
                        continue

                    word = self.get_word_by_id(word_id)
                    if word and word.get_word_position():
                        results.append((word.get_word_position(), word))

        results.sort(key=lambda x: (x[0].surah, x[0].ayah, x[0].position))
        return results

    def get_word_count(
        self, surah: Optional[int] = None, ayah: Optional[int] = None
    ) -> int:
        self._load_word_data()

        count = 0
        for word_id, pos_data in self._word_positions.items():
            if surah is not None and pos_data["surah"] != surah:
                continue
            if ayah is not None and pos_data["ayah"] != ayah:
                continue
            count += 1

        return count

    @lru_cache(maxsize=128)
    def get_verse_word_count(self, surah: int, ayah: int) -> int:
        return self.get_word_count(surah, ayah)

    def get_all_word_positions(self) -> Dict[str, WordPosition]:
        self._load_word_data()

        positions = {}
        for word_id, pos_data in self._word_positions.items():
            positions[word_id] = WordPosition(
                surah=pos_data["surah"],
                ayah=pos_data["ayah"],
                position=pos_data["position"],
            )

        return positions
