import json
import os
from typing import Dict, List, Optional

from alifbaa.ir.book import Book, BookMetadata
from alifbaa.ir.chapter import Chapter
from alifbaa.ir.verse import Verse
from alifbaa.workers.schema_validator import validate_data


class BookRepository:
    def __init__(self, data_dir: str):
        self.data_dir = data_dir
        self._book_metadata: Dict[str, BookMetadata] = {}
        self._books: Dict[str, Book] = {}
        self._load_book_metadata()

    def _load_book_metadata(self) -> None:
        for filename in os.listdir(self.data_dir):
            if filename.endswith(".json"):
                book_path = os.path.join(self.data_dir, filename)
                try:
                    with open(book_path, "r", encoding="utf-8") as f:
                        book_data = json.load(f)
                        validation_error = validate_data(book_data, "book_schema.json")
                        if validation_error:
                            print(f"Error validating {book_path}: {validation_error}")
                            continue

                        book_id = book_data["metadata"]["id"]
                        self._book_metadata[book_id] = BookMetadata.from_json(
                            book_data["metadata"]
                        )
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON in {book_path}: {e}")

    def _load_book(self, book_id: str) -> Book:
        if book_id in self._books:
            return self._books[book_id]

        for filename in os.listdir(self.data_dir):
            if filename.endswith(".json"):
                book_path = os.path.join(self.data_dir, filename)
                try:
                    with open(book_path, "r", encoding="utf-8") as f:
                        book_data = json.load(f)
                        validation_error = validate_data(book_data, "book_schema.json")
                        if validation_error:
                            print(f"Error validating {book_path}: {validation_error}")
                            continue

                        if book_data["metadata"]["id"] == book_id:
                            book = Book.from_json(book_data)
                            self._books[book_id] = book
                            return book
                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON in {book_path}: {e}")
                except Exception as e:
                    print(f"Error processing {book_path}: {e}")

        raise KeyError(f"Book with ID {book_id} not found")

    def get_available_books(self) -> List[BookMetadata]:
        return list(self._book_metadata.values())

    def get_book_metadata(self, book_id: str) -> Optional[BookMetadata]:
        return self._book_metadata.get(book_id)

    def get_book(self, book_id: str) -> Optional[Book]:
        try:
            return self._load_book(book_id)
        except KeyError:
            return None

    def get_chapter(self, book_id: str, chapter_num: int) -> Optional[Chapter]:
        book = self.get_book(book_id)
        if not book:
            return None

        return book.chapters_by_number.get(chapter_num)

    def get_verse(
        self, book_id: str, chapter_num: int, verse_num: int
    ) -> Optional[Verse]:
        chapter = self.get_chapter(book_id, chapter_num)
        if not chapter:
            return None

        return chapter.verses_by_number.get(verse_num)
