from dataclasses import dataclass, field
from functools import cached_property
from types import MappingProxyType
from typing import Dict, Optional, Tuple, Any, Mapping

from alifbaa.ir.chapter import Chapter


@dataclass(frozen=True)
class BookMetadata:
    id: str
    title: str
    language: str
    author: Optional[str] = None
    book_type: Optional[str] = None
    requires_word_parsing: bool = False
    chapters_count: int = 0
    verses_count: int = 0

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "BookMetadata":
        return cls(
            id=data["id"],
            title=data["title"],
            language=data["language"],
            author=data.get("author"),
            book_type=data.get("book_type"),
            requires_word_parsing=data.get("requires_word_parsing", False),
            chapters_count=data.get("chapters_count", 0),
            verses_count=data.get("verses_count", 0),
        )

    def to_json(self) -> Dict[str, Any]:
        result = {
            "id": self.id,
            "title": self.title,
            "language": self.language,
            "requires_word_parsing": self.requires_word_parsing,
            "chapters_count": self.chapters_count,
            "verses_count": self.verses_count,
        }

        if self.author:
            result["author"] = self.author

        if self.book_type:
            result["book_type"] = self.book_type

        return result


@dataclass(frozen=True)
class Book:
    metadata: BookMetadata
    chapters: Tuple[Chapter, ...] = field(default_factory=tuple)

    @cached_property
    def chapters_by_number(self) -> Mapping[int, Chapter]:
        return MappingProxyType({c.number: c for c in self.chapters})

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "Book":
        metadata = BookMetadata.from_json(data["metadata"])
        chapters = tuple(Chapter.from_json(c) for c in data.get("chapters", []))
        return cls(metadata=metadata, chapters=chapters)

    def to_json(self) -> Dict[str, Any]:
        return {
            "metadata": self.metadata.to_json(),
            "chapters": [chapter.to_json() for chapter in self.chapters],
        }
