from typing import <PERSON><PERSON>

from pyarabic import araby  # type: ignore

from alifbaa.ir.word import Word
from alifbaa.ir.verse import Verse
from alifbaa.repositories.parser_repositories import ParserRepository


class PyArabicParserRepository(ParserRepository):
    def parse_words(self, verse: Verse) -> Tuple[Word, ...]:
        words = []
        tokens = araby.tokenize_with_location(verse.text)
        tokens_plain = araby.tokenize_with_location(verse.text_plain)
        for i, (token_info, token_plain_info) in enumerate(zip(tokens, tokens_plain)):
            word_text = token_info["token"]
            word_text_plain = token_plain_info["token"]
            words.append(Word(text=word_text, position=i, text_plain=word_text_plain))

        return tuple(words)

    def remove_tashkeel(self, text: str) -> str:
        return araby.strip_tashkeel(text)
