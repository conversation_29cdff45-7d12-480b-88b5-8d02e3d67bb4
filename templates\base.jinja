<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Alifbaa{% endblock %}</title>
    <style>
        body {
            font-family: {{ current_font or 'Amiri Quran' }};
            font-size: {{ current_font_size or 24 }}px;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .arabic-text {
            font-family: {{ current_font or 'Amiri Quran' }};
            font-size: {{ current_font_size or 24 }}px;
            line-height: 1.8;
            direction: rtl;
        }
        
        .word {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .word:hover {
            background-color: #f0f0f0;
        }
        
        .verse {
            margin: 10px 0;
            padding: 10px;
            border-left: 3px solid #007bff;
        }
        
        .verse-number {
            font-weight: bold;
            color: #666;
            margin-left: 10px;
        }
        
        .chapter-header {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .navigation {
            margin: 20px 0;
            text-align: center;
        }
        
        .navigation a {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        
        .navigation a:hover {
            background: #0056b3;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
            direction: rtl;
        }
        
        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: black;
        }
    </style>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
</head>
<body>
    <div class="container">
        {% block content %}{% endblock %}
    </div>
    
    <!-- Word Details Modal -->
    <div id="wordModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="wordModalContent">
                <!-- Word details will be loaded here -->
            </div>
        </div>
    </div>
    
    <script>
        // Modal functionality
        const modal = document.getElementById('wordModal');
        const span = document.getElementsByClassName('close')[0];
        
        span.onclick = function() {
            modal.style.display = 'none';
        }
        
        window.onclick = function(event) {
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }
        
        // Word click handler
        function showWordDetails(wordText, wordTextPlain, verseNum, position) {
            const formData = new FormData();
            formData.append('word_text', wordText);
            formData.append('word_text_plain', wordTextPlain);
            formData.append('verse_num', verseNum);
            formData.append('position', position);
            
            fetch('/word-details', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                document.getElementById('wordModalContent').innerHTML = html;
                modal.style.display = 'block';
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    </script>
</body>
</html>
