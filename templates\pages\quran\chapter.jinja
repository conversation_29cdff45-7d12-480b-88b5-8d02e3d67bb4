{% extends "base.jinja" %}

{% block title %}{{ chapter.name }} - سورة رقم {{ chapter.number }}{% endblock %}

{% block content %}
<div class="quran-chapter">
    <div class="navigation">
        <a href="/quran">العودة إلى القائمة</a>
        {% if chapter.number > 1 %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number - 1 }}">السورة السابقة</a>
        {% endif %}
        {% if chapter.number < 114 %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number + 1 }}">السورة التالية</a>
        {% endif %}
    </div>
    
    <div class="chapter-header">
        <div class="chapter-number">{{ chapter.number }}</div>
        <h1 class="chapter-name arabic-text">{{ chapter.name }}</h1>
        {% if chapter.translations and chapter.translations.get('fr') %}
        <div class="chapter-translation">{{ chapter.translations.fr }}</div>
        {% endif %}
        <div class="chapter-info">
            {% if chapter.verses %}
            <span>{{ chapter.verses|length }} آية</span>
            {% endif %}
        </div>
    </div>
    
    <div id="settings-panel">
        {% include "components/settings_panel.jinja" %}
    </div>
    
    <div class="verses-container">
        {% if chapter.verses %}
            {% for verse in chapter.verses %}
            <div class="verse">
                {% include "components/verse_content.jinja" %}
            </div>
            {% endfor %}
        {% else %}
            <div class="no-verses">
                <p>لا توجد آيات متاحة</p>
            </div>
        {% endif %}
    </div>
    
    <div class="navigation">
        <a href="/quran">العودة إلى القائمة</a>
        {% if chapter.number > 1 %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number - 1 }}">السورة السابقة</a>
        {% endif %}
        {% if chapter.number < 114 %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number + 1 }}">السورة التالية</a>
        {% endif %}
    </div>
</div>

<style>
.quran-chapter {
    direction: rtl;
}

.chapter-header {
    text-align: center;
    margin: 30px 0;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
}

.chapter-number {
    background: rgba(255,255,255,0.2);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 24px;
    margin-bottom: 15px;
}

.chapter-name {
    font-size: 36px;
    margin: 15px 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.chapter-translation {
    font-size: 18px;
    opacity: 0.9;
    margin: 10px 0;
    direction: ltr;
}

.chapter-info {
    font-size: 16px;
    opacity: 0.8;
    margin-top: 15px;
}

.verses-container {
    margin: 30px 0;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.verse {
    margin: 20px 0;
}

.no-verses {
    text-align: center;
    padding: 40px;
    color: #666;
}

.navigation {
    position: sticky;
    top: 20px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin: 20px 0;
    z-index: 100;
}
</style>
{% endblock %}
