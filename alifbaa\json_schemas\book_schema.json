{"$schema": "https://json-schema.org/draft/2020-12/schema", "type": "object", "properties": {"metadata": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "author": {"type": "string"}, "book_type": {"type": "string"}, "language": {"type": "string"}, "requires_word_parsing": {"type": "boolean"}, "chapters_count": {"type": "integer", "minimum": 0}, "verses_count": {"type": "integer", "minimum": 0}}, "required": ["id", "title", "language", "requires_word_parsing", "chapters_count", "verses_count"]}, "chapters": {"type": "array", "items": {"type": "object", "properties": {"number": {"type": "integer", "minimum": 0}, "name": {"type": "string"}, "translations": {"type": "object", "properties": {"en": {"type": "string"}, "fr": {"type": "string"}}, "additionalProperties": {"type": "string"}}, "verses": {"type": "array", "items": {"type": "object", "properties": {"number": {"type": "integer", "minimum": 0}, "text": {"type": "string"}, "text_plain": {"type": "string"}, "translations": {"type": "object", "properties": {"en": {"type": "string"}, "fr": {"type": "string"}}, "additionalProperties": {"type": "string"}}}, "required": ["number", "text", "translations"]}}}, "required": ["number", "name", "translations", "verses"]}}}, "required": ["metadata", "chapters"]}