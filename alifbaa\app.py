from flask import Flask
import os

from alifbaa.config import get_config_file, get_flask_config
from alifbaa.routes.web import web_bp


def create_app() -> Flask:
    # Set template directory to the root templates folder
    template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates")
    app = Flask(__name__, template_folder=template_dir)

    flask_config = get_flask_config()
    app.config["ALIFBAA_CONFIG"] = get_config_file()
    app.config["SECRET_KEY"] = flask_config["secret_key"]
    app.register_blueprint(web_bp)

    return app


if __name__ == "__main__":
    app = create_app()
    app.run(debug=True)
