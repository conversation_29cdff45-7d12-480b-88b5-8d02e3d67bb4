from flask import Flask

from alifbaa.config import get_config_file, get_flask_config
from alifbaa.routes.web import web_bp


def create_app() -> Flask:
    app = Flask(__name__)

    flask_config = get_flask_config()
    app.config["ALIFBAA_CONFIG"] = get_config_file()
    app.config["SECRET_KEY"] = flask_config["secret_key"]
    app.register_blueprint(web_bp)

    return app


if __name__ == "__main__":
    app = create_app()
    app.run(debug=True)
