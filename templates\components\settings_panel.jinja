<div class="settings-panel">
    <h3>إعدادات النص</h3>
    
    <form hx-post="/update-settings" hx-target="#settings-panel" hx-swap="outerHTML">
        <div class="setting-group">
            <label for="font">الخط:</label>
            <select name="font" id="font">
                {% for font in fonts %}
                <option value="{{ font }}" {% if font == current_font %}selected{% endif %}>
                    {{ font }}
                </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="setting-group">
            <label for="font_size">حجم الخط:</label>
            <input type="range" 
                   name="font_size" 
                   id="font_size" 
                   min="16" 
                   max="48" 
                   value="{{ current_font_size }}"
                   oninput="updateFontSizeDisplay(this.value)">
            <span id="font-size-display">{{ current_font_size }}px</span>
        </div>
        
        <div class="setting-group">
            <label for="show_diacritics">
                <input type="checkbox" 
                       name="show_diacritics" 
                       id="show_diacritics" 
                       value="true"
                       {% if show_diacritics %}checked{% endif %}>
                إظهار التشكيل
            </label>
        </div>
        
        <div class="setting-group">
            <button type="submit">تطبيق الإعدادات</button>
        </div>
    </form>
</div>

<script>
function updateFontSizeDisplay(value) {
    document.getElementById('font-size-display').textContent = value + 'px';
}
</script>

<style>
.settings-panel {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    direction: rtl;
}

.settings-panel h3 {
    margin-top: 0;
    color: #333;
}

.setting-group {
    margin: 15px 0;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.setting-group select,
.setting-group input[type="range"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
}

.setting-group input[type="checkbox"] {
    margin-left: 8px;
}

.setting-group button {
    background: #28a745;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: inherit;
    font-size: 16px;
}

.setting-group button:hover {
    background: #218838;
}

#font-size-display {
    margin-right: 10px;
    font-weight: bold;
    color: #007bff;
}
</style>
