from typing import Tuple, Dict, List, FrozenSet
from functools import lru_cache
from pyarabic import araby  # type: ignore
from alifbaa.repositories.parser_repositories import ParserRepository
from alifbaa.ir.arabic_char import ArabicCharType
from alifbaa.ir.word import Word
from alifbaa.ir.verse import Verse


class TextOperations:
    def __init__(self, parser_repository: ParserRepository):
        self.parser_repository = parser_repository
        self._NON_CONNECTABLE_LEFT: FrozenSet[str] = frozenset(
            [
                araby.ALEF,
                araby.DAL,
                araby.THAL,
                araby.REH,
                araby.ZAIN,
                araby.WAW,
                araby.ALEF_WASLA,
            ]
        )
        self._char_type_map = self._build_char_type_map()

    def _build_char_type_map(self) -> Dict[str, ArabicCharType]:
        char_type_map: Dict[str, ArabicCharType] = {}

        for char in araby.TASHKEEL:
            char_type_map[char] = ArabicCharType.DIACRITIC

        char_type_map[araby.TATWEEL] = ArabicCharType.SPECIAL

        for char in araby.LETTERS:
            if char in self._NON_CONNECTABLE_LEFT:
                char_type_map[char] = ArabicCharType.NON_CONNECTABLE_LEFT
            else:
                char_type_map[char] = ArabicCharType.CONNECTABLE

        return char_type_map

    def _get_char_type(self, char: str) -> ArabicCharType:
        if araby.is_haraka(char):
            return ArabicCharType.DIACRITIC
        elif araby.is_tashkeel(char):
            return ArabicCharType.DIACRITIC
        elif araby.is_tatweel(char):
            return ArabicCharType.SPECIAL
        elif char in self._char_type_map:
            return self._char_type_map[char]
        else:
            return ArabicCharType.CONNECTABLE

    @lru_cache(maxsize=1000)
    def parse_words(self, verse: Verse) -> Tuple[Word, ...]:
        # Using lru_cache for performance - Verse now uses frozendict for translations
        return self.parser_repository.parse_words(verse)

    def remove_diacritics(self, text: str) -> str:
        return self.parser_repository.remove_tashkeel(text)

    def add_tatweel(self, word: Word, num_tatweels: int = 1) -> str:
        """
        Add tatweel (kashida) to Arabic text in a Word object.

        Rules:
        - Tatweel is added between connectable letters
        - Tatweel is not added after non-connectable letters (ا, د, ذ, ر, ز, و, ٱ)
        - Tatweel is placed after the letter+diacritic combination
        - If text already has tatweel, it's preserved
        - Multiple tatweels can be added at each position

        Args:
            word: A Word object containing the text to process
            num_tatweels: Number of tatweels to add at each position (default: 1)

        Returns:
            The text with tatweel added
        """
        return self._add_tatweel_to_text(word.text, num_tatweels)

    def _add_tatweel_to_text(self, text: str, num_tatweels: int = 1) -> str:
        if not text or len(text) <= 1 or num_tatweels <= 0 or araby.TATWEEL in text:
            return text

        result = []
        i = 0

        while i < len(text):
            current_char = text[i]
            result.append(current_char)
            if i == len(text) - 1:
                break

            if self._is_diacritic_or_special(current_char):
                i += 1
                continue
            next_idx = self._process_diacritics(text, i + 1, result)
            if next_idx >= len(text):
                break

            next_char = text[next_idx]
            if self._should_add_tatweel(current_char, next_char):
                result.extend([araby.TATWEEL] * num_tatweels)

            i = next_idx

        return "".join(result)

    def _is_diacritic_or_special(self, char: str) -> bool:
        char_type = self._get_char_type(char)
        return char_type in (ArabicCharType.DIACRITIC, ArabicCharType.SPECIAL)

    def _process_diacritics(self, text: str, start_idx: int, result: List[str]) -> int:
        idx = start_idx
        while (
            idx < len(text)
            and self._get_char_type(text[idx]) == ArabicCharType.DIACRITIC
        ):
            result.append(text[idx])
            idx += 1
        return idx

    def _should_add_tatweel(self, current_char: str, next_char: str) -> bool:
        return (
            current_char not in self._NON_CONNECTABLE_LEFT
            and next_char in araby.LETTERS
            and self._get_char_type(next_char) != ArabicCharType.SPECIAL
        )
