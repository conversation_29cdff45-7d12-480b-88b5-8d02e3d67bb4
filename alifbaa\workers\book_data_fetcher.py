from typing import Dict, List, Optional, Any

from alifbaa.use_cases.book_display_use_case import BookDisplayUseCase
from alifbaa.repositories.book_repository import BookRepository
from alifbaa.config import get_books_config
from alifbaa.workers.text_operations import TextOperations
from alifbaa.repositories.parsers.pyarabic_parser import PyArabicParserRepository
from alifbaa.ir.book import Book, BookMetadata
from alifbaa.ir.chapter import Chapter
from functools import lru_cache


@lru_cache(maxsize=None)
def get_book_display_use_case(config_path: str) -> BookDisplayUseCase:
    books_config = get_books_config(config_path)
    books_dir = books_config["books_dir"]
    repo = BookRepository(books_dir)
    parser = PyArabicParserRepository()
    text_operations = TextOperations(parser)
    return BookDisplayUseCase(repo, text_operations)


def get_available_books(config: str) -> List[BookMetadata]:
    return get_book_display_use_case(config).get_available_books()


def get_book_metadata(book_id: str, config: str) -> Optional[BookMetadata]:
    return get_book_display_use_case(config).get_book_metadata(book_id)


def get_book(book_id: str, config: str) -> Optional[Book]:
    return get_book_display_use_case(config).get_book(book_id)


def get_book_json(book_id: str, config: str) -> Optional[Dict[str, Any]]:
    return get_book_display_use_case(config).get_book_json(book_id)


def get_chapter(book_id: str, chapter_num: int, config: str) -> Optional[Chapter]:
    return get_book_display_use_case(config).get_chapter(book_id, chapter_num)


def get_chapter_json(
    book_id: str, chapter_num: int, config: str
) -> Optional[Dict[str, Any]]:
    return get_book_display_use_case(config).get_chapter_json(book_id, chapter_num)


def get_chapter_data(
    book_id: str, chapter_num: int, config: str, show_diacritics: bool = True
) -> Optional[Chapter]:
    return get_book_display_use_case(config).get_chapter_data(
        book_id, chapter_num, show_diacritics
    )


def get_verse_data(
    book_id: str,
    chapter_num: int,
    verse_num: int,
    config: str,
    show_diacritics: bool = True,
) -> Optional[Dict[str, Any]]:
    return get_book_display_use_case(config).get_verse_data(
        book_id, chapter_num, verse_num, show_diacritics
    )


def get_verse(
    book_id: str,
    chapter_num: int,
    verse_num: int,
    config: str,
    show_diacritics: bool = True,
) -> Optional[Dict[str, Any]]:
    return get_book_display_use_case(config).get_verse_data(
        book_id, chapter_num, verse_num, show_diacritics
    )
