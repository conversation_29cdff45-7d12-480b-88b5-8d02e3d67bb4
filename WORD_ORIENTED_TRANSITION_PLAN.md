# Word-Oriented Architecture Transition Plan

## Overview

This document outlines the plan to transition the current verse-oriented codebase to a word-oriented architecture where verses are reconstituted from words, making the app fundamentally word-oriented.

## Current State Analysis

### Current Data Flow (Verse-Oriented)

```
Book ID → Chapter → Verse → Parse text into words → Display
```

### Desired Data Flow (Word-Oriented)

```
Word queries → Load words → Group by verse → Reconstitute verses → Display
```

### Current Architecture Issues

1. **Verse-centric data loading**: Words are parsed from verse text at display time
2. **Inefficient word access**: No direct word querying capabilities
3. **Parsing overhead**: Text parsing happens repeatedly for the same verses
4. **Limited word-level functionality**: Hard to implement word-specific features
5. **Data structure mismatch**: JSON supports word-oriented access but code doesn't leverage it

## Transition Strategy

### Phase 1: Foundation (High Priority)

#### 1.1 Enhanced Word Repository Layer

- Create dedicated `WordRepository` for direct word access
- Implement word querying by position, surah, ayah
- Add word range queries for verse reconstitution
- Cache word data for performance

#### 1.2 Word-Oriented IR Enhancements

- Add word indexing capabilities to existing Word class
- Create `WordPosition` dataclass for precise positioning
- Add word relationship tracking (next/previous word)
- Implement word-to-verse mapping utilities

#### 1.3 Verse Reconstitution Logic

- Create `VerseReconstitution` service
- Implement word aggregation algorithms
- Add verse metadata preservation
- Handle word ordering and spacing

### Phase 2: Repository Transformation (Medium Priority)

#### 2.1 Book Repository Modernization

- Refactor `BookRepository` to use word-based loading
- Implement lazy verse loading from words
- Add word-based caching strategies
- Maintain backward compatibility during transition

#### 2.2 Data Access Optimization

- Update `BookDataFetcher` for word-oriented queries
- Implement efficient word batch loading
- Add word indexing for fast lookups
- Optimize memory usage for large datasets

### Phase 3: Use Cases Refactoring (Medium Priority)

#### 3.1 Book Display Use Case Updates

- Refactor `BookDisplayUseCase` to load words first
- Implement verse reconstitution in display logic
- Add word-level display features
- Maintain existing API contracts

#### 3.2 New Word-Oriented Use Cases

- Create `WordSearchUseCase` for word-level queries
- Implement `WordAnalysisUseCase` for linguistic analysis
- Add `WordNavigationUseCase` for word-based navigation

### Phase 4: Interface Updates (Low Priority)

#### 4.1 Route Enhancements

- Add word-specific endpoints to web routes
- Implement word search and navigation APIs
- Maintain existing verse-oriented endpoints
- Add word-level metadata endpoints

#### 4.2 Performance Optimizations

- Implement word-based pagination
- Add intelligent caching for reconstituted verses
- Optimize JSON data loading strategies

## Detailed Implementation Plan

### 1. Create Word Repository (Phase 1.1)

#### New File: `alifbaa/repositories/word_repository.py`

```python
from typing import List, Optional, Tuple, Dict
from dataclasses import dataclass
from alifbaa.ir.word import Word

@dataclass
class WordPosition:
    surah: int
    ayah: int
    position: int

class WordRepository:
    """Repository for direct word access and queries."""

    def get_word_by_position(self, surah: int, ayah: int, position: int) -> Optional[Word]:
        """Get a specific word by its position."""

    def get_words_for_verse(self, surah: int, ayah: int) -> List[Word]:
        """Get all words for a specific verse."""

    def get_words_range(self, start_pos: WordPosition, end_pos: WordPosition) -> List[Word]:
        """Get words within a position range."""

    def search_words_by_text(self, text: str, surah: Optional[int] = None) -> List[Tuple[WordPosition, Word]]:
        """Search for words by text content."""

    def get_word_count(self, surah: Optional[int] = None, ayah: Optional[int] = None) -> int:
        """Get total word count for surah/ayah or entire book."""
```

#### Enhancement: `alifbaa/ir/word.py`

```python
# Add to existing Word dataclass
@dataclass
class Word:
    # ...existing fields...

    # New fields for word-oriented architecture
    surah: int
    ayah: int
    position_in_verse: int
    global_position: int  # Unique word ID across entire book

    def get_position_key(self) -> str:
        """Get unique position identifier."""
        return f"{self.surah}:{self.ayah}:{self.position_in_verse}"
```

### 2. Verse Reconstitution Service (Phase 1.3)

#### New File: `alifbaa/workers/verse_reconstitution.py`

```python
from typing import List, Dict
from alifbaa.ir.word import Word
from alifbaa.ir.verse import Verse

class VerseReconstitutionService:
    """Service for reconstituting verses from words."""

    def reconstitute_verse(self, words: List[Word], verse_metadata: Dict) -> Verse:
        """Create a verse from a list of words."""

    def reconstitute_verses_batch(self, words_by_verse: Dict[Tuple[int, int], List[Word]],
                                 metadata_by_verse: Dict) -> List[Verse]:
        """Efficiently reconstitute multiple verses."""

    def calculate_verse_text(self, words: List[Word]) -> Tuple[str, str]:
        """Calculate verse text and text_plain from words."""
```

### 3. Repository Integration (Phase 2.1)

#### Update: `alifbaa/repositories/book_repository.py`

```python
class BookRepository:
    def __init__(self, word_repository: WordRepository,
                 reconstitution_service: VerseReconstitutionService):
        self.word_repository = word_repository
        self.reconstitution_service = reconstitution_service
        # ...existing init...

    def get_verse(self, book_id: str, chapter_num: int, verse_num: int) -> Optional[Verse]:
        """Get verse by reconstituting from words."""
        words = self.word_repository.get_words_for_verse(chapter_num, verse_num)
        if not words:
            return None

        # Get verse metadata from existing data
        verse_metadata = self._get_verse_metadata(book_id, chapter_num, verse_num)
        return self.reconstitution_service.reconstitute_verse(words, verse_metadata)
```

### 4. Use Case Updates (Phase 3.1)

#### Update: `alifbaa/use_cases/book_display_use_case.py`

```python
class BookDisplayUseCase:
    def __init__(self, book_repository: BookRepository, word_repository: WordRepository):
        self.book_repository = book_repository
        self.word_repository = word_repository
        # ...existing init...

    def get_chapter_with_word_metadata(self, book_id: str, chapter_num: int) -> ChapterWithWordData:
        """Get chapter with enhanced word-level metadata."""
        # Load all words for chapter first
        words_by_verse = self._load_chapter_words(book_id, chapter_num)

        # Reconstitute verses from words
        verses = self._reconstitute_chapter_verses(words_by_verse)

        # Add word-level enhancements
        return self._enhance_with_word_metadata(verses)
```

## Implementation Priorities

### High Priority (Immediate Implementation)

1. **WordRepository creation** - Enables direct word access
2. **Word IR enhancements** - Adds positioning and indexing
3. **VerseReconstitutionService** - Core reconstitution logic
4. **Basic integration tests** - Ensures functionality works

### Medium Priority (Next Sprint)

1. **BookRepository refactoring** - Transition to word-based loading
2. **BookDisplayUseCase updates** - Leverage word-oriented data
3. **Performance optimizations** - Caching and efficient queries
4. **Comprehensive testing** - Full test coverage

### Low Priority (Future Enhancements)

1. **New word-oriented use cases** - Search, analysis, navigation
2. **Route enhancements** - Word-specific endpoints
3. **Advanced features** - Word highlighting, linguistic analysis
4. **UI improvements** - Word-level interactions

## Migration Strategy

### Backward Compatibility

- Do not worry about it, make sure new implementation works

### Data Migration

- No JSON data changes required (already word-oriented)
- Add word indexing for performance
- Create word-to-verse mapping caches
- Optimize data loading patterns

### Testing Strategy

- Unit tests for all new word-oriented components
- Integration tests for verse reconstitution
- Performance tests for word queries
- Regression tests for existing functionality

## Success Metrics

### Performance Improvements

- Faster word-level queries (target: <10ms)
- Reduced memory usage for large chapters
- Improved caching efficiency
- Better scalability for word operations

### Functionality Enhancements

- Direct word access capabilities
- Efficient verse reconstitution
- Word-level search and navigation
- Enhanced linguistic analysis features

### Code Quality Improvements

- Better separation of concerns
- More testable architecture
- Reduced coupling between components
- Improved maintainability

## Risk Mitigation

### Technical Risks

- **Performance regression**: Comprehensive benchmarking before/after
- **Data consistency**: Extensive testing of reconstitution logic
- **Memory usage**: Monitor and optimize word loading patterns
- **Backward compatibility**: Maintain existing API contracts

### Implementation Risks

- **Complexity increase**: Gradual rollout with feature flags
- **Team coordination**: Clear documentation and communication
- **Testing coverage**: Comprehensive test suite for all changes
- **Rollback plan**: Ability to revert to verse-oriented approach

## Next Steps

1. **Review and approve this plan** with the development team
2. **Create detailed tickets** for Phase 1 implementation
3. **Set up development branch** for word-oriented features
4. **Begin implementation** with WordRepository and basic tests
5. **Establish benchmarks** for current performance
6. **Create migration timeline** with specific milestones

This transition will fundamentally improve the app's architecture while maintaining all existing functionality and enabling powerful new word-oriented features.
