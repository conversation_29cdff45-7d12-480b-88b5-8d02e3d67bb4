{% extends "base.jinja" %}

{% block title %}خطأ - Error{% endblock %}

{% block content %}
<div class="error-page">
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h1>خطأ في التطبيق</h1>
        <h2>Application Error</h2>
        
        <div class="error-message">
            <p><strong>الرسالة:</strong> {{ error_message or "حدث خطأ غير متوقع" }}</p>
            <p><strong>Message:</strong> {{ error_message or "An unexpected error occurred" }}</p>
        </div>
        
        <div class="error-actions">
            <a href="/quran" class="btn btn-primary">محاولة الوصول للقرآن</a>
            <a href="/" class="btn btn-secondary">العودة للرئيسية</a>
        </div>
        
        <div class="error-help">
            <h3>استكشاف الأخطاء - Troubleshooting</h3>
            <ul>
                <li>تأكد من وجود ملف التكوين <code>local_config.cfg</code></li>
                <li>تأكد من وجود بيانات القرآن في المجلد المحدد</li>
                <li>تحقق من صحة مسار البيانات في ملف التكوين</li>
            </ul>
            <ul>
                <li>Ensure <code>local_config.cfg</code> file exists</li>
                <li>Verify Quran data exists in the specified directory</li>
                <li>Check data path configuration is correct</li>
            </ul>
        </div>
    </div>
</div>

<style>
.error-page {
    direction: rtl;
    text-align: center;
    padding: 40px 20px;
}

.error-container {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.error-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.error-container h1 {
    color: #dc3545;
    margin: 20px 0 10px 0;
    font-size: 28px;
}

.error-container h2 {
    color: #6c757d;
    margin: 0 0 30px 0;
    font-size: 20px;
    font-weight: normal;
}

.error-message {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 30px 0;
    text-align: right;
}

.error-message p {
    margin: 10px 0;
    color: #495057;
}

.error-actions {
    margin: 30px 0;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    margin: 0 10px;
    text-decoration: none;
    border-radius: 6px;
    font-weight: bold;
    transition: background-color 0.2s;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.error-help {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #dee2e6;
    text-align: right;
}

.error-help h3 {
    color: #495057;
    margin-bottom: 20px;
}

.error-help ul {
    text-align: right;
    margin: 15px 0;
    padding-right: 20px;
}

.error-help li {
    margin: 8px 0;
    color: #6c757d;
}

.error-help code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #e83e8c;
}
</style>
{% endblock %}
