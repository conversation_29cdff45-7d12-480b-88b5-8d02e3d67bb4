{% extends "base.jinja" %}

{% block title %}الآية {{ verse.number }} - {{ chapter.name }}{% endblock %}

{% block content %}
<div class="quran-verse">
    <div class="navigation">
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number }}">العودة إلى السورة</a>
        <a href="/quran">العودة إلى القائمة</a>
        {% if verse.number > 1 %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number }}/verse/{{ verse.number - 1 }}">الآية السابقة</a>
        {% endif %}
        {% if verse.number < chapter.verses|length %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number }}/verse/{{ verse.number + 1 }}">الآية التالية</a>
        {% endif %}
    </div>
    
    <div class="chapter-context">
        <h2 class="chapter-name arabic-text">{{ chapter.name }}</h2>
        <div class="chapter-info">
            سورة رقم {{ chapter.number }}
            {% if chapter.translations and chapter.translations.get('fr') %}
            - {{ chapter.translations.fr }}
            {% endif %}
        </div>
    </div>
    
    <div id="settings-panel">
        {% include "components/settings_panel.jinja" %}
    </div>
    
    <div class="verse-container">
        <div class="verse-display">
            {% include "components/verse_content.jinja" %}
        </div>
    </div>
    
    <div class="verse-analysis">
        <h3>تحليل الآية</h3>
        {% if verse.words %}
        <div class="words-grid">
            {% for word in verse.words %}
            <div class="word-card" 
                 onclick="showWordDetails('{{ word.text }}', '{{ word.text_plain }}', '{{ verse.number }}', '{{ word.position }}')">
                <div class="word-text arabic-text">
                    {% if show_diacritics %}{{ word.text }}{% else %}{{ word.text_plain }}{% endif %}
                </div>
                <div class="word-position">كلمة {{ word.position + 1 }}</div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <p>لا يوجد تحليل للكلمات متاح</p>
        {% endif %}
    </div>
    
    <div class="navigation">
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number }}">العودة إلى السورة</a>
        <a href="/quran">العودة إلى القائمة</a>
        {% if verse.number > 1 %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number }}/verse/{{ verse.number - 1 }}">الآية السابقة</a>
        {% endif %}
        {% if verse.number < chapter.verses|length %}
        <a href="/book/{{ book_id }}/chapter/{{ chapter.number }}/verse/{{ verse.number + 1 }}">الآية التالية</a>
        {% endif %}
    </div>
</div>

<style>
.quran-verse {
    direction: rtl;
}

.chapter-context {
    text-align: center;
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.chapter-name {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 10px;
}

.chapter-info {
    color: #666;
    font-size: 16px;
}

.verse-container {
    margin: 30px 0;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.verse-display {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-right: 4px solid #007bff;
}

.verse-analysis {
    margin: 30px 0;
}

.verse-analysis h3 {
    color: #2c3e50;
    margin-bottom: 20px;
}

.words-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.word-card {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.word-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.word-text {
    font-size: 24px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.word-position {
    font-size: 12px;
    color: #666;
}
</style>
{% endblock %}
